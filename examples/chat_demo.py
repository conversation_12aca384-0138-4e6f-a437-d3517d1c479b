#!/usr/bin/env python3
"""
Featureimanbug AI - Chat interface demo.

This script demonstrates the chat interface functionality.
"""
import sys
import os
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

import torch
import tiktoken
from src.config import GPT_CONFIG_SMALL
from src.model.gpt import GPTModel
from src.generation.generator import TextGenerator
from src.chat.interface import ChatInterface


def main():
    """Chat interface demo."""
    print("💬 Featureimanbug AI - Chat Interface Demo")
    print("=" * 50)
    
    # Create a small model for demo (normally you'd load a trained model)
    print("🧠 Creating demo model...")
    config = GPT_CONFIG_SMALL
    model = GPTModel(config)
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    model.eval()
    
    print(f"🖥️  Using device: {device}")
    
    # Initialize components
    tokenizer = tiktoken.get_encoding("gpt2")
    generator = TextGenerator(model, tokenizer)
    
    # Create chat interface
    chat_interface = ChatInterface(
        model=model,
        generator=generator,
        history_dir="examples/chat_history",
        auto_save=True
    )
    
    print("✅ Chat interface initialized")
    print("\n" + "=" * 50)
    print("🤖 Featureimanbug AI Chat Demo")
    print("=" * 50)
    print("Note: This is using an untrained model, so responses will be random.")
    print("In a real scenario, you would load a trained model.")
    print("Type 'quit' to exit the demo.")
    print("=" * 50)
    
    # Demo conversation
    demo_messages = [
        "Hello! How are you today?",
        "What can you tell me about artificial intelligence?",
        "Can you help me write a short story?",
        "What's the weather like?",
        "Tell me a joke!"
    ]
    
    print("\n🎭 Running automated demo with sample messages...")
    print("(In real usage, you would type your own messages)")
    
    for i, message in enumerate(demo_messages, 1):
        print(f"\n👤 User: {message}")
        
        try:
            # Generate response
            response = chat_interface.send_message(message)
            print(f"🤖 Featureimanbug AI: {response}")
            
        except Exception as e:
            print(f"❌ Error generating response: {e}")
        
        if i < len(demo_messages):
            input("Press Enter to continue to next message...")
    
    print("\n" + "=" * 50)
    print("📊 Chat Statistics:")
    stats = chat_interface.get_statistics()
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    print("\n💾 Conversation History:")
    conversations = chat_interface.get_conversation_list()
    for conv in conversations:
        print(f"  📄 {conv['title']} ({conv['message_count']} messages)")
    
    print("\n✅ Demo completed!")
    print("💡 To use the full interactive chat, run: python main.py chat")


if __name__ == "__main__":
    main()
