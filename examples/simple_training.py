#!/usr/bin/env python3
"""
Featureimanbug AI - Simple training example.

This script demonstrates how to train a small GPT model on text data.
"""
import sys
import os
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

import torch
import tiktoken
from src.config import GPT_CONFIG_SMALL
from src.model.gpt import GPTModel
from src.training.trainer import GPTTrainer
from src.training.dataset import create_simple_dataloader


def main():
    """Simple training example."""
    print("🚀 Featureimanbug AI - Simple Training Example")
    print("=" * 50)
    
    # Sample text data (replace with your own data)
    sample_text = """
    The quick brown fox jumps over the lazy dog. This is a sample text for training 
    a language model. In the world of artificial intelligence, language models have 
    become increasingly important for various natural language processing tasks.
    
    Machine learning and deep learning have revolutionized the field of AI. 
    Transformers, in particular, have shown remarkable success in language understanding 
    and generation tasks. The attention mechanism allows models to focus on relevant 
    parts of the input sequence.
    
    Training a language model requires careful consideration of various factors such as 
    model architecture, training data, optimization techniques, and evaluation metrics. 
    The goal is to create a model that can understand and generate human-like text.
    """ * 100  # Repeat to have more training data
    
    # Configuration
    config = GPT_CONFIG_SMALL
    print(f"📊 Model configuration: {config.n_layers} layers, {config.emb_dim} dimensions")
    
    # Create model
    model = GPTModel(config)
    print(f"🧠 Created model with {model.get_num_params():,} parameters")
    
    # Create tokenizer and dataloader
    tokenizer = tiktoken.get_encoding("gpt2")
    dataloader = create_simple_dataloader(
        text=sample_text,
        tokenizer=tokenizer,
        batch_size=4,  # Small batch size for demo
        max_length=128,  # Shorter sequences for faster training
        stride=64
    )
    
    print(f"📦 Created dataloader with {len(dataloader)} batches")
    
    # Create trainer
    trainer = GPTTrainer(
        model=model,
        config=config,
        checkpoint_dir="examples/checkpoints",
        log_interval=5,  # Log more frequently for demo
        save_interval=50  # Save more frequently for demo
    )
    
    # Train for a few epochs
    print("🎯 Starting training...")
    try:
        for epoch in range(3):  # Just 3 epochs for demo
            print(f"\n📚 Epoch {epoch + 1}/3")
            trainer.current_epoch = epoch
            
            epoch_metrics = trainer.train_epoch(dataloader, epoch)
            
            print(f"✅ Epoch {epoch + 1} completed")
            print(f"   Average loss: {epoch_metrics['avg_loss']:.4f}")
            print(f"   Time: {epoch_metrics['epoch_time']:.2f}s")
            
            # Save checkpoint after each epoch
            trainer.save_checkpoint(epoch, epoch_metrics['avg_loss'])
        
        print("\n🎉 Training completed successfully!")
        print("💾 Model saved in examples/checkpoints/")
        
        # Test generation
        print("\n🧪 Testing text generation...")
        model.eval()
        
        with torch.no_grad():
            # Simple generation test
            prompt = "The future of artificial intelligence"
            input_ids = torch.tensor(
                tokenizer.encode(prompt), 
                dtype=torch.long
            ).unsqueeze(0)
            
            generated = model.generate(
                input_ids, 
                max_new_tokens=50,
                temperature=0.8
            )
            
            generated_text = tokenizer.decode(generated[0].tolist())
            print(f"📝 Generated text: {generated_text}")
        
    except KeyboardInterrupt:
        print("\n⏹️  Training interrupted by user")
        trainer.save_checkpoint(trainer.current_epoch, 0.0, filename="interrupted_checkpoint.pt")
        print("💾 Checkpoint saved")
    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
