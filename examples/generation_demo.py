#!/usr/bin/env python3
"""
Featureimanbug AI - Text generation demo.

This script demonstrates different text generation strategies.
"""
import sys
import os
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

import torch
import tiktoken
from src.config import GPT_CONFIG_SMALL
from src.model.gpt import GPTModel
from src.generation.generator import TextGenerator


def main():
    """Text generation demo."""
    print("✨ Featureimanbug AI - Text Generation Demo")
    print("=" * 60)
    
    # Create model
    print("🧠 Creating demo model...")
    config = GPT_CONFIG_SMALL
    model = GPTModel(config)
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    model.eval()
    
    print(f"🖥️  Using device: {device}")
    
    # Initialize generator
    tokenizer = tiktoken.get_encoding("gpt2")
    generator = TextGenerator(model, tokenizer)
    
    print("✅ Generator initialized")
    print("\nNote: This demo uses an untrained model, so outputs will be random.")
    print("In practice, you would load a trained model for meaningful generation.")
    
    # Demo prompt
    prompt = "The future of artificial intelligence is"
    
    print(f"\n📝 Demo prompt: '{prompt}'")
    print("=" * 60)
    
    # Different generation strategies
    strategies = [
        {
            'name': 'Greedy Decoding',
            'params': {
                'strategy': 'greedy',
                'max_new_tokens': 50
            },
            'description': 'Always selects the most likely token'
        },
        {
            'name': 'Temperature Sampling (Low)',
            'params': {
                'strategy': 'temperature',
                'temperature': 0.3,
                'max_new_tokens': 50
            },
            'description': 'Low temperature for more focused output'
        },
        {
            'name': 'Temperature Sampling (High)',
            'params': {
                'strategy': 'temperature',
                'temperature': 1.2,
                'max_new_tokens': 50
            },
            'description': 'High temperature for more creative output'
        },
        {
            'name': 'Top-k Sampling',
            'params': {
                'strategy': 'top_k',
                'top_k': 20,
                'temperature': 0.8,
                'max_new_tokens': 50
            },
            'description': 'Sample from top 20 most likely tokens'
        },
        {
            'name': 'Top-p (Nucleus) Sampling',
            'params': {
                'strategy': 'top_p',
                'top_p': 0.9,
                'temperature': 0.8,
                'max_new_tokens': 50
            },
            'description': 'Sample from tokens with cumulative probability ≤ 0.9'
        }
    ]
    
    # Generate with each strategy
    for i, strategy_config in enumerate(strategies, 1):
        print(f"\n{i}. {strategy_config['name']}")
        print(f"   {strategy_config['description']}")
        print("-" * 40)
        
        try:
            generated_text = generator.generate(
                prompt=prompt,
                **strategy_config['params']
            )
            
            # Extract just the generated part
            if prompt in generated_text:
                generated_part = generated_text[len(prompt):].strip()
                print(f"🤖 Generated: {generated_part}")
            else:
                print(f"🤖 Generated: {generated_text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        if i < len(strategies):
            input("\nPress Enter to try next strategy...")
    
    print("\n" + "=" * 60)
    print("🧪 Advanced Features Demo")
    print("=" * 60)
    
    # Streaming generation demo
    print("\n📡 Streaming Generation Demo:")
    print("(Tokens appear one by one as they're generated)")
    print("-" * 40)
    print(f"Prompt: {prompt}")
    print("Generated: ", end="", flush=True)
    
    try:
        for chunk in generator._stream_generate(
            torch.tensor(tokenizer.encode(prompt), device=device).unsqueeze(0),
            30,  # max tokens
            generator.strategies['temperature'](0.8),
            1.0,  # repetition penalty
            None  # stop tokens
        ):
            print(chunk, end="", flush=True)
        print()  # New line
        
    except Exception as e:
        print(f"\n❌ Streaming error: {e}")
    
    # Perplexity calculation demo
    print("\n📊 Perplexity Calculation Demo:")
    test_text = "The quick brown fox jumps over the lazy dog."
    try:
        perplexity = generator.calculate_perplexity(test_text)
        print(f"Text: '{test_text}'")
        print(f"Perplexity: {perplexity:.2f}")
        print("(Lower perplexity indicates better model fit)")
    except Exception as e:
        print(f"❌ Perplexity calculation error: {e}")
    
    print("\n✅ Demo completed!")
    print("💡 To generate your own text, run: python main.py generate --prompt 'Your prompt here'")


if __name__ == "__main__":
    main()
