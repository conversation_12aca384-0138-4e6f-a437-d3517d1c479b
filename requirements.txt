# Core dependencies for Featureimanbug AI
torch >= 2.3.0             # Deep learning framework
tiktoken >= 0.5.1          # Tokenization
tqdm >= 4.66.1             # Progress bars
numpy >= 1.26, < 2.1       # Numerical computing

# Web interface dependencies
flask >= 2.3.0             # Web framework
flask-socketio >= 5.3.0    # Real-time communication
flask-cors >= 4.0.0        # Cross-origin requests

# Training dependencies
wandb >= 0.16.0            # Experiment tracking
matplotlib >= 3.7.1        # Plotting and visualization
seaborn >= 0.12.0          # Statistical plotting
datasets >= 2.14.0         # Hugging Face datasets
transformers >= 4.35.0     # For tokenizers and utilities

# Optional dependencies
jupyterlab >= 4.0          # Jupyter notebooks (optional)
pandas >= 2.2.1            # Data manipulation (optional)
psutil >= 5.9.5            # System monitoring (optional)
