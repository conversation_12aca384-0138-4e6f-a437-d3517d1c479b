# 🌐 Featureimanbug AI Web Interface Setup Guide

This guide will help you set up and run the web-based chat interface for Featureimanbug AI using your GPT-2 355M model.

## 🚀 Quick Start

### 1. Install Dependencies

First, install the required dependencies:

```bash
pip install -r requirements.txt
```

### 2. Convert Your GPT-2 Model

Convert your TensorFlow GPT-2 model to PyTorch format for faster loading:

```bash
python main.py convert
```

This will:
- Load your GPT-2 355M model from `gpt2/355M/`
- Convert TensorFlow weights to PyTorch format
- Save the converted model to `models/gpt2_355m_converted.pt`
- Test the model to ensure it works correctly

### 3. Start the Web Interface

Launch the web-based chat interface:

```bash
python main.py web
```

Or use the direct web server script:

```bash
python3 web_server.py
```

### 4. Open in Browser

Open your web browser and go to:
- **http://localhost:5000** (default)
- **http://127.0.0.1:5000**

## 🎯 Features

### 💬 **Chat Interface**
- **Real-time messaging** with streaming responses
- **Conversation history** with persistent storage
- **Multiple conversations** - create, switch, and manage
- **Responsive design** - works on desktop and mobile

### ⚙️ **Customizable Settings**
- **Temperature** - Control creativity (0.1-2.0)
- **Top-k sampling** - Limit token choices (1-100)
- **Top-p sampling** - Nucleus sampling (0.1-1.0)
- **Max tokens** - Response length (10-500)
- **Generation strategy** - Choose sampling method

### 📊 **Advanced Features**
- **Conversation search** - Find past conversations
- **Export functionality** - Save conversations as JSON/TXT
- **Model status monitoring** - Real-time model information
- **Auto-save** - Conversations saved automatically

## 🔧 Configuration Options

### Command Line Options

```bash
python main.py web --help
```

Available options:
- `--host` - Host to bind to (default: 127.0.0.1)
- `--port` - Port to bind to (default: 5000)
- `--model-path` - Custom model path
- `--debug` - Enable debug mode
- `--no-auto-load` - Start without loading model

### Examples

```bash
# Start on different port
python main.py web --port 8080

# Enable debug mode
python main.py web --debug

# Use custom model
python main.py web --model-path /path/to/your/model.pt

# Bind to all interfaces (accessible from other devices)
python main.py web --host 0.0.0.0
```

## 📁 File Structure

After setup, your project structure will include:

```
AIbYME/
├── src/web/                      # Web interface code
│   ├── templates/index.html      # Main HTML template
│   ├── static/
│   │   ├── css/style.css        # Styles
│   │   └── js/app.js            # JavaScript application
│   ├── app.py                   # Flask application
│   ├── routes.py                # API endpoints
│   ├── events.py                # WebSocket events
│   └── model_manager.py         # Model management
├── models/                       # Converted models
│   └── gpt2_355m_converted.pt   # Your converted model
├── web_chat_history/             # Web chat conversations
├── web_server.py                 # Main web server script
└── main.py                       # Updated main script
```

## 🛠️ Troubleshooting

### Model Loading Issues

**Problem**: "No model found" error
```bash
❌ No model found!
```

**Solution**: Convert your GPT-2 model first:
```bash
python main.py convert
```

**Problem**: TensorFlow import error
```bash
❌ TensorFlow is required to convert GPT-2 weights
```

**Solution**: Install TensorFlow:
```bash
pip install tensorflow
```

### Web Interface Issues

**Problem**: Port already in use
```bash
❌ Address already in use
```

**Solution**: Use a different port:
```bash
python main.py web --port 8080
```

**Problem**: Can't access from other devices
**Solution**: Bind to all interfaces:
```bash
python main.py web --host 0.0.0.0
```

### Performance Issues

**Problem**: Slow response generation
**Solutions**:
1. Reduce `max_new_tokens` in settings
2. Use GPU if available (CUDA)
3. Lower `temperature` for faster generation

**Problem**: High memory usage
**Solutions**:
1. Close other applications
2. Reduce model context length
3. Use CPU instead of GPU if memory limited

## 🎨 Customization

### Modify the Interface

1. **Styling**: Edit `src/web/static/css/style.css`
2. **Layout**: Edit `src/web/templates/index.html`
3. **Functionality**: Edit `src/web/static/js/app.js`

### Add New Features

1. **API Endpoints**: Add to `src/web/routes.py`
2. **WebSocket Events**: Add to `src/web/events.py`
3. **Model Features**: Modify `src/web/model_manager.py`

## 🔒 Security Notes

### For Personal Use
- Default settings bind to `127.0.0.1` (localhost only)
- No authentication required for personal use
- Conversations stored locally

### For Network Access
If you want to access from other devices:
```bash
python main.py web --host 0.0.0.0
```

**Security considerations**:
- No built-in authentication
- All users can access all conversations
- Consider adding authentication for multi-user scenarios

## 📱 Mobile Support

The interface is responsive and works on mobile devices:
- **Touch-friendly** buttons and inputs
- **Responsive layout** adapts to screen size
- **Mobile keyboard** support for text input
- **Swipe gestures** for sidebar navigation

## 🚀 Advanced Usage

### Multiple Models

To switch between different models:

1. Convert multiple models:
```bash
# Convert different model sizes
python scripts/convert_gpt2.py --input gpt2/124M --output models/gpt2_124m.pt
python scripts/convert_gpt2.py --input gpt2/355M --output models/gpt2_355m.pt
```

2. Start with specific model:
```bash
python main.py web --model-path models/gpt2_124m.pt
```

### Development Mode

For development with auto-reload:
```bash
python main.py web --debug
```

This enables:
- **Auto-reload** on code changes
- **Detailed error messages**
- **Debug logging**

## 🎉 Enjoy Your AI Assistant!

You now have a fully functional web-based ChatGPT-like interface running locally with your GPT-2 355M model!

### Next Steps:
1. **Experiment** with different generation settings
2. **Create** multiple conversations for different topics
3. **Export** interesting conversations
4. **Customize** the interface to your liking

### Support:
- Check the console output for error messages
- Review the troubleshooting section above
- Modify settings through the web interface

**Happy chatting with Featureimanbug AI! 🤖✨**
