# Check GPU availability
import torch
print(f"🖥️  CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"📊 GPU: {torch.cuda.get_device_name()}")
    print(f"💾 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
else:
    print("⚠️  No GPU detected. Training will be slow on CPU.")

# Mount Google Drive for persistent storage
from google.colab import drive
drive.mount('/content/drive')

# Create directories
import os
os.makedirs('/content/drive/MyDrive/featureimanbug_checkpoints', exist_ok=True)
os.makedirs('/content/data', exist_ok=True)

print("✅ Google Drive mounted and directories created")

# Clone the Featureimanbug AI repository
!git clone https://github.com/your-username/AIbYME.git /content/AIbYME
# Or upload your code manually

# Change to project directory
%cd /content/AIbYME

print("✅ Repository cloned")

# Install requirements
!pip install -q torch tiktoken tqdm numpy matplotlib seaborn datasets transformers wandb
!pip install -q flask flask-socketio flask-cors  # For web interface compatibility

print("✅ Dependencies installed")

# Fix import issues by updating the trainer file
import os

# Fix the trainer.py file
trainer_file = '/content/AIbYME/src/training/trainer.py'
if os.path.exists(trainer_file):
    # Read the file
    with open(trainer_file, 'r') as f:
        content = f.read()
    
    # Replace old imports with new ones
    content = content.replace(
        'from ..model.gpt import GPTModel',
        'from ..model.featureimanbug_model import FeatureimanbugModel'
    )
    content = content.replace(
        'from ..config import GPTConfig',
        'from ..model.featureimanbug_model import FeatureimanbugConfig'
    )
    content = content.replace('GPTModel', 'FeatureimanbugModel')
    content = content.replace('GPTConfig', 'FeatureimanbugConfig')
    
    # Write back the fixed file
    with open(trainer_file, 'w') as f:
        f.write(content)
    
    print("✅ Fixed trainer.py imports")

# Also fix any other files with similar issues
for root, dirs, files in os.walk('/content/AIbYME/src'):
    for file in files:
        if file.endswith('.py'):
            filepath = os.path.join(root, file)
            try:
                with open(filepath, 'r') as f:
                    content = f.read()
                
                if 'from ..model.gpt import GPTModel' in content:
                    content = content.replace(
                        'from ..model.gpt import GPTModel',
                        'from ..model.featureimanbug_model import FeatureimanbugModel'
                    )
                    content = content.replace('GPTModel', 'FeatureimanbugModel')
                    
                    with open(filepath, 'w') as f:
                        f.write(content)
                    print(f"✅ Fixed {filepath}")
            except:
                pass

print("🔧 Import fixes completed!")

# Complete fix for all import issues
import os
import sys

# Add paths
sys.path.append('/content/AIbYME/src')
sys.path.append('/content/AIbYME')
sys.path.append('/content/AIbYME/training')

# Remove problematic old files that are causing conflicts
problematic_files = [
    '/content/AIbYME/src/model/gpt.py',
    '/content/AIbYME/src/training/trainer.py',
    '/content/AIbYME/src/training/optimizer.py'
]

for file_path in problematic_files:
    if os.path.exists(file_path):
        os.remove(file_path)
        print(f"🗑️  Removed problematic file: {file_path}")

# Create a simple __init__.py file to fix package issues
init_files = [
    '/content/AIbYME/src/__init__.py',
    '/content/AIbYME/src/model/__init__.py',
    '/content/AIbYME/training/__init__.py'
]

for init_file in init_files:
    os.makedirs(os.path.dirname(init_file), exist_ok=True)
    with open(init_file, 'w') as f:
        f.write('# Package init file\n')
    print(f"✅ Created: {init_file}")

print("🔧 Cleanup completed!")

# Import all necessary modules and define missing functions
import sys
import os
import torch
import tiktoken
import numpy as np
from torch.utils.data import Dataset, DataLoader

# Add paths for local development
current_dir = os.getcwd()
if '/content/AIbYME' not in sys.path:
    sys.path.append('/content/AIbYME')
if '/content/AIbYME/src' not in sys.path:
    sys.path.append('/content/AIbYME/src')
if current_dir not in sys.path:
    sys.path.append(current_dir)
if os.path.join(current_dir, 'src') not in sys.path:
    sys.path.append(os.path.join(current_dir, 'src'))

# Try to import the model classes
try:
    from src.model.featureimanbug_model import FeatureimanbugModel, FeatureimanbugConfig
    print("✅ Imported FeatureimanbugModel and FeatureimanbugConfig")
except ImportError:
    try:
        from model.featureimanbug_model import FeatureimanbugModel, FeatureimanbugConfig
        print("✅ Imported FeatureimanbugModel and FeatureimanbugConfig (alternative path)")
    except ImportError:
        print("❌ Could not import FeatureimanbugModel. Please check the model file exists.")
        # Define a minimal config class as fallback
        class FeatureimanbugConfig:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)

# Try to import the trainer
try:
    from training.colab_trainer import ColabTrainer
    print("✅ Imported ColabTrainer")
except ImportError:
    print("❌ Could not import ColabTrainer. Please check the trainer file exists.")
    # Define a minimal trainer class as fallback
    class ColabTrainer:
        def __init__(self, **kwargs):
            print("Using fallback trainer")

# Define dataset class
class SimpleTextDataset(Dataset):
    def __init__(self, input_ids, target_ids):
        self.input_ids = [torch.tensor(ids, dtype=torch.long) for ids in input_ids]
        self.target_ids = [torch.tensor(ids, dtype=torch.long) for ids in target_ids]
    
    def __len__(self):
        return len(self.input_ids)
    
    def __getitem__(self, idx):
        return self.input_ids[idx], self.target_ids[idx]

# Define helper functions
def optimize_batch_size_for_colab(max_length=1024, emb_dim=768, n_layers=12, available_memory_gb=12.0):
    """
    Automatically determine optimal batch size for Colab.
    """
    vocab_size = 50257  # GPT-2 vocab size
    
    # Try different batch sizes
    for batch_size in [32, 16, 8, 4, 2, 1]:
        # Rough memory estimation
        model_params = (
            vocab_size * emb_dim +  # Token embeddings
            max_length * emb_dim +  # Position embeddings
            n_layers * (
                3 * emb_dim * emb_dim +  # QKV projections
                emb_dim * emb_dim +      # Output projection
                4 * emb_dim * emb_dim +  # Feed-forward
                4 * emb_dim              # Layer norms
            ) +
            vocab_size * emb_dim  # Output head
        )
        
        # Memory estimates (rough)
        model_memory = model_params * 4 / 1e9  # 4 bytes per float32
        activation_memory = batch_size * max_length * emb_dim * n_layers * 4 / 1e9
        gradient_memory = model_memory
        optimizer_memory = model_memory * 2
        
        total_memory = model_memory + activation_memory + gradient_memory + optimizer_memory
        
        if total_memory < available_memory_gb * 0.8:  # 80% safety margin
            print(f"🎯 Recommended batch size: {batch_size}")
            print(f"   Estimated memory usage: {total_memory:.1f} GB")
            return batch_size
    
    print("⚠️  Warning: Even batch size 1 might be too large!")
    return 1

def prepare_training_data(data_source, tokenizer, max_length=1024, stride=512, train_split=0.9, cache_dir=None):
    """Prepare training data from various sources."""
    print(f"📚 Preparing data from: {data_source}")
    
    if data_source == "wikitext":
        # Simple sample text for testing
        sample_text = """
        Artificial intelligence (AI) is intelligence demonstrated by machines, in contrast to the natural intelligence displayed by humans and animals. Leading AI textbooks define the field as the study of "intelligent agents": any device that perceives its environment and takes actions that maximize its chance of successfully achieving its goals. Colloquially, the term "artificial intelligence" is often used to describe machines that mimic "cognitive" functions that humans associate with the human mind, such as "learning" and "problem solving".
        
        The scope of AI is disputed: as machines become increasingly capable, tasks considered to require "intelligence" are often removed from the definition of AI, a phenomenon known as the AI effect. A quip in Tesler's Theorem says "AI is whatever hasn't been done yet." For instance, optical character recognition is frequently excluded from things considered to be AI, having become a routine technology.
        
        Modern machine learning techniques are at the core of AI. Problems for AI applications include reasoning, knowledge representation, planning, learning, natural language processing, perception, and the ability to move and manipulate objects. General intelligence is among the field's long-term goals.
        """ * 50  # Repeat for more training data
        
    else:
        # Use sample text or file path
        if os.path.exists(data_source):
            with open(data_source, 'r', encoding='utf-8') as f:
                sample_text = f.read()
        else:
            sample_text = data_source
    
    # Simple tokenization and dataset creation
    tokens = tokenizer.encode(sample_text)
    print(f"📊 Total tokens: {len(tokens):,}")
    
    # Split into train/validation
    if train_split < 1.0:
        split_idx = int(len(tokens) * train_split)
        train_tokens = tokens[:split_idx]
        val_tokens = tokens[split_idx:]
    else:
        train_tokens = tokens
        val_tokens = []
    
    # Create datasets
    def create_sequences(token_list, max_length, stride):
        input_ids = []
        target_ids = []
        
        for i in range(0, len(token_list) - max_length, stride):
            input_chunk = token_list[i:i + max_length]
            target_chunk = token_list[i + 1:i + max_length + 1]
            
            if len(input_chunk) == max_length and len(target_chunk) == max_length:
                input_ids.append(input_chunk)
                target_ids.append(target_chunk)
        
        return SimpleTextDataset(input_ids, target_ids)
    
    train_dataset = create_sequences(train_tokens, max_length, stride)
    val_dataset = create_sequences(val_tokens, max_length, stride) if val_tokens else None
    
    print(f"📦 Created {len(train_dataset)} training sequences")
    if val_dataset:
        print(f"📦 Created {len(val_dataset)} validation sequences")
    
    return train_dataset, val_dataset

def create_dataloader(dataset, batch_size=8, shuffle=True, num_workers=2, pin_memory=True, drop_last=True):
    """Create a DataLoader for training."""
    return DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        pin_memory=pin_memory,
        drop_last=drop_last
    )

print("✅ All modules and functions loaded successfully!")

# Model Configuration
config = FeatureimanbugConfig(
    vocab_size=50257,
    context_length=512,    # Reduced for Colab
    emb_dim=512,          # Smaller than GPT-2
    n_heads=8,
    n_layers=8,
    drop_rate=0.1,
    
    # Enhanced features
    use_rotary_pos_emb=True,
    use_glu_activation=True,
    use_layer_scale=True,
    use_pre_norm=True,
    
    # Training settings
    learning_rate=3e-4,
    batch_size=4,         # Will be optimized automatically
    max_epochs=10,
    warmup_steps=500,
    weight_decay=0.01
)

# Optimize batch size for available memory
optimal_batch_size = optimize_batch_size_for_colab(
    max_length=config.context_length,
    emb_dim=config.emb_dim,
    n_layers=config.n_layers
)
config.batch_size = optimal_batch_size

print(f"📊 Model Configuration:")
print(f"   Layers: {config.n_layers}")
print(f"   Embedding dim: {config.emb_dim}")
print(f"   Context length: {config.context_length}")
print(f"   Batch size: {config.batch_size}")
print(f"   Enhanced features: RoPE, GLU, LayerScale, PreNorm")

# Choose your data source
# Options:
# 1. "wikitext" - WikiText dataset (good for general language modeling)
# 2. "openwebtext" - OpenWebText subset (diverse web text)
# 3. "file:/path/to/your/file.txt" - Custom text file
# 4. Raw text string

DATA_SOURCE = "wikitext"  # Change this to your preferred data source

# For custom files, upload them to Colab and specify the path:
# DATA_SOURCE = "file:/content/your_text_file.txt"

print(f"📖 Using data source: {DATA_SOURCE}")

# Initialize tokenizer
tokenizer = tiktoken.get_encoding("gpt2")
print(f"🔤 Tokenizer vocabulary size: {tokenizer.n_vocab}")

# Prepare datasets
print("📚 Preparing training data...")
train_dataset, val_dataset = prepare_training_data(
    data_source=DATA_SOURCE,
    tokenizer=tokenizer,
    max_length=config.context_length,
    stride=config.context_length // 2,
    train_split=0.9,
    cache_dir="/content/data"
)

print(f"✅ Training dataset: {len(train_dataset)} sequences")
if val_dataset:
    print(f"✅ Validation dataset: {len(val_dataset)} sequences")
else:
    print("ℹ️  No validation dataset (using all data for training)")

# Create data loaders
train_dataloader = create_dataloader(
    train_dataset,
    batch_size=config.batch_size,
    shuffle=True,
    num_workers=2,  # Limited for Colab
    pin_memory=True
)

val_dataloader = None
if val_dataset:
    val_dataloader = create_dataloader(
        val_dataset,
        batch_size=config.batch_size,
        shuffle=False,
        num_workers=2,
        pin_memory=True
    )

print(f"📦 Training batches: {len(train_dataloader)}")
if val_dataloader:
    print(f"📦 Validation batches: {len(val_dataloader)}")

# Estimate training time
total_steps = len(train_dataloader) * config.max_epochs
print(f"🕐 Estimated total training steps: {total_steps:,}")

# Optional: Set up Weights & Biases for experiment tracking
# Uncomment and run this cell if you want to use W&B

# import wandb
# wandb.login()  # You'll need to enter your W&B API key

USE_WANDB = False  # Set to True if you want to use W&B
print(f"📊 Weights & Biases: {'Enabled' if USE_WANDB else 'Disabled'}")

# Initialize trainer
trainer = ColabTrainer(
    config=config,
    model_name="featureimanbug-ai-v1",
    checkpoint_dir="/content/drive/MyDrive/featureimanbug_checkpoints",
    use_wandb=USE_WANDB,
    wandb_project="featureimanbug-ai"
)

print("✅ Trainer initialized")
print(f"🧠 Model parameters: {trainer.model.get_num_params():,}")

# Start training
print("🚀 Starting training...")
print("💡 Tip: Training will automatically save checkpoints to Google Drive")
print("💡 Tip: You can interrupt and resume training anytime")
print("\n" + "="*60)

try:
    trainer.train(
        train_dataloader=train_dataloader,
        val_dataloader=val_dataloader,
        resume_from_checkpoint=True  # Automatically resume if checkpoint exists
    )
    
    print("\n🎉 Training completed successfully!")
    
except KeyboardInterrupt:
    print("\n⏹️  Training interrupted by user")
    print("💾 Checkpoint saved - you can resume training later")
    
except Exception as e:
    print(f"\n❌ Training failed: {e}")
    import traceback
    traceback.print_exc()

# Load the best model
best_model_path = "/content/drive/MyDrive/featureimanbug_checkpoints/featureimanbug-ai-v1_best.pt"

if os.path.exists(best_model_path):
    print("📂 Loading best model...")
    trainer.load_checkpoint(best_model_path)
    print("✅ Best model loaded")
else:
    print("ℹ️  Using current model (no best checkpoint found)")

# Set model to evaluation mode
trainer.model.eval()
print("🧪 Model ready for evaluation")

# Test text generation
test_prompts = [
    "The future of artificial intelligence",
    "Once upon a time",
    "In the world of technology",
    "Hello, how are you",
    "The benefits of machine learning"
]

print("🧪 Testing text generation...")
print("="*60)

for i, prompt in enumerate(test_prompts, 1):
    print(f"\n{i}. Prompt: '{prompt}'")
    print("-" * 40)
    
    # Simple character-level generation for testing
    # In a real implementation, you'd use proper tokenization
    try:
        generated_text = trainer.generate_sample(prompt, max_tokens=50)
        print(f"Generated: {generated_text}")
    except Exception as e:
        print(f"Generation failed: {e}")

print("\n✅ Generation testing completed")

# Plot final training results
print("📈 Plotting training results...")
trainer.plot_training_progress()

# Print training summary
print("\n📊 Training Summary:")
print(f"   Total epochs: {trainer.current_epoch}")
print(f"   Total steps: {trainer.current_step}")
print(f"   Best loss: {trainer.best_loss:.4f}")
print(f"   Final training loss: {trainer.training_losses[-1]:.4f}" if trainer.training_losses else "N/A")
if trainer.validation_losses:
    print(f"   Final validation loss: {trainer.validation_losses[-1]:.4f}")

# Export model for web interface
export_path = "/content/drive/MyDrive/featureimanbug_ai_web_model.pt"

# Create export package
export_package = {
    'model_state_dict': trainer.model.state_dict(),
    'config': config.__dict__,
    'model_type': 'featureimanbug_ai',
    'version': '1.0.0',
    'training_info': {
        'epochs': trainer.current_epoch,
        'steps': trainer.current_step,
        'best_loss': trainer.best_loss,
        'final_loss': trainer.training_losses[-1] if trainer.training_losses else None
    }
}

torch.save(export_package, export_path)
print(f"💾 Model exported for web interface: {export_path}")

# Also save to local Colab storage for download
local_export_path = "/content/featureimanbug_ai_model.pt"
torch.save(export_package, local_export_path)
print(f"💾 Model also saved locally: {local_export_path}")

print("\n📋 To use this model with the web interface:")
print("1. Download the model file from Google Drive or Colab")
print("2. Place it in your AIbYME/models/ directory")
print("3. Update the web interface to load this model")
print("4. Start the web interface: python web_server.py")