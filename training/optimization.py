"""
Featureimanbug AI - Advanced Optimization Utilities

This module provides state-of-the-art optimization techniques for training
the Featureimanbug AI model, including modern optimizers and schedulers.
"""
import torch
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import _LRScheduler
import math
from typing import Optional, List, Dict, Any


class CosineWarmupScheduler(_LRScheduler):
    """
    Cosine annealing scheduler with linear warmup.
    
    This scheduler combines linear warmup with cosine annealing for better
    training dynamics and convergence.
    """
    
    def __init__(self, 
                 optimizer: torch.optim.Optimizer,
                 warmup_steps: int,
                 total_steps: int,
                 min_lr_ratio: float = 0.1,
                 last_epoch: int = -1):
        """
        Initialize the scheduler.
        
        Args:
            optimizer: Optimizer to schedule
            warmup_steps: Number of warmup steps
            total_steps: Total number of training steps
            min_lr_ratio: Minimum learning rate as ratio of initial LR
            last_epoch: Last epoch number
        """
        self.warmup_steps = warmup_steps
        self.total_steps = total_steps
        self.min_lr_ratio = min_lr_ratio
        super().__init__(optimizer, last_epoch)
    
    def get_lr(self) -> List[float]:
        """Calculate learning rate for current step."""
        if self.last_epoch < self.warmup_steps:
            # Linear warmup
            lr_scale = self.last_epoch / self.warmup_steps
        else:
            # Cosine annealing
            progress = (self.last_epoch - self.warmup_steps) / (self.total_steps - self.warmup_steps)
            lr_scale = self.min_lr_ratio + (1 - self.min_lr_ratio) * 0.5 * (1 + math.cos(math.pi * progress))
        
        return [base_lr * lr_scale for base_lr in self.base_lrs]


class AdamWWithDecay(optim.AdamW):
    """
    Enhanced AdamW optimizer with improved weight decay handling.
    
    This optimizer provides better weight decay handling for transformer models
    by excluding certain parameters from weight decay.
    """
    
    def __init__(self, 
                 model: nn.Module,
                 lr: float = 3e-4,
                 betas: tuple = (0.9, 0.95),
                 eps: float = 1e-8,
                 weight_decay: float = 0.01,
                 exclude_from_decay: Optional[List[str]] = None):
        """
        Initialize the optimizer.
        
        Args:
            model: Model to optimize
            lr: Learning rate
            betas: Adam beta parameters
            eps: Adam epsilon parameter
            weight_decay: Weight decay coefficient
            exclude_from_decay: Parameter names to exclude from weight decay
        """
        if exclude_from_decay is None:
            exclude_from_decay = ['bias', 'norm', 'emb']
        
        # Separate parameters
        decay_params = []
        no_decay_params = []
        
        for name, param in model.named_parameters():
            if param.requires_grad:
                if any(nd in name for nd in exclude_from_decay):
                    no_decay_params.append(param)
                else:
                    decay_params.append(param)
        
        # Create parameter groups
        param_groups = [
            {'params': decay_params, 'weight_decay': weight_decay},
            {'params': no_decay_params, 'weight_decay': 0.0}
        ]
        
        super().__init__(param_groups, lr=lr, betas=betas, eps=eps)


class GradientClipper:
    """
    Advanced gradient clipping with multiple strategies.
    
    Provides both norm-based and value-based gradient clipping for stable training.
    """
    
    def __init__(self, 
                 max_norm: float = 1.0,
                 norm_type: float = 2.0,
                 clip_value: Optional[float] = None):
        """
        Initialize the gradient clipper.
        
        Args:
            max_norm: Maximum gradient norm
            norm_type: Type of norm to use
            clip_value: Maximum gradient value (optional)
        """
        self.max_norm = max_norm
        self.norm_type = norm_type
        self.clip_value = clip_value
    
    def clip_gradients(self, model: nn.Module) -> float:
        """
        Clip gradients and return the gradient norm.
        
        Args:
            model: Model whose gradients to clip
            
        Returns:
            Gradient norm before clipping
        """
        # Get gradient norm before clipping
        total_norm = 0.0
        for param in model.parameters():
            if param.grad is not None:
                param_norm = param.grad.data.norm(self.norm_type)
                total_norm += param_norm.item() ** self.norm_type
        
        total_norm = total_norm ** (1.0 / self.norm_type)
        
        # Apply gradient clipping
        if self.clip_value is not None:
            # Value-based clipping
            torch.nn.utils.clip_grad_value_(model.parameters(), self.clip_value)
        
        # Norm-based clipping
        torch.nn.utils.clip_grad_norm_(model.parameters(), self.max_norm, self.norm_type)
        
        return total_norm


class EMA:
    """
    Exponential Moving Average for model parameters.
    
    Maintains a moving average of model parameters for better generalization.
    """
    
    def __init__(self, model: nn.Module, decay: float = 0.999):
        """
        Initialize EMA.
        
        Args:
            model: Model to track
            decay: EMA decay rate
        """
        self.decay = decay
        self.shadow = {}
        self.backup = {}
        
        # Initialize shadow parameters
        for name, param in model.named_parameters():
            if param.requires_grad:
                self.shadow[name] = param.data.clone()
    
    def update(self, model: nn.Module):
        """Update EMA parameters."""
        for name, param in model.named_parameters():
            if param.requires_grad and name in self.shadow:
                self.shadow[name] = (
                    self.decay * self.shadow[name] + 
                    (1.0 - self.decay) * param.data
                )
    
    def apply_shadow(self, model: nn.Module):
        """Apply EMA parameters to model."""
        for name, param in model.named_parameters():
            if param.requires_grad and name in self.shadow:
                self.backup[name] = param.data.clone()
                param.data = self.shadow[name]
    
    def restore(self, model: nn.Module):
        """Restore original parameters."""
        for name, param in model.named_parameters():
            if param.requires_grad and name in self.backup:
                param.data = self.backup[name]
        self.backup = {}


def create_optimizer(model: nn.Module,
                    learning_rate: float = 3e-4,
                    weight_decay: float = 0.01,
                    optimizer_type: str = 'adamw') -> torch.optim.Optimizer:
    """
    Create an optimized optimizer for transformer training.
    
    Args:
        model: Model to optimize
        learning_rate: Learning rate
        weight_decay: Weight decay coefficient
        optimizer_type: Type of optimizer
        
    Returns:
        Optimizer instance
    """
    if optimizer_type.lower() == 'adamw':
        return AdamWWithDecay(
            model,
            lr=learning_rate,
            weight_decay=weight_decay,
            betas=(0.9, 0.95),  # Better for transformers
            eps=1e-8
        )
    elif optimizer_type.lower() == 'adam':
        # Separate parameters for weight decay
        decay_params = []
        no_decay_params = []
        
        for name, param in model.named_parameters():
            if param.requires_grad:
                if any(nd in name for nd in ['bias', 'norm', 'emb']):
                    no_decay_params.append(param)
                else:
                    decay_params.append(param)
        
        param_groups = [
            {'params': decay_params, 'weight_decay': weight_decay},
            {'params': no_decay_params, 'weight_decay': 0.0}
        ]
        
        return optim.Adam(param_groups, lr=learning_rate, betas=(0.9, 0.95), eps=1e-8)
    else:
        raise ValueError(f"Unknown optimizer type: {optimizer_type}")


def create_scheduler(optimizer: torch.optim.Optimizer,
                    warmup_steps: int = 1000,
                    total_steps: int = 10000,
                    scheduler_type: str = 'cosine_warmup',
                    min_lr_ratio: float = 0.1) -> Optional[_LRScheduler]:
    """
    Create a learning rate scheduler.
    
    Args:
        optimizer: Optimizer instance
        warmup_steps: Number of warmup steps
        total_steps: Total number of training steps
        scheduler_type: Type of scheduler
        min_lr_ratio: Minimum learning rate ratio
        
    Returns:
        Scheduler instance or None
    """
    if scheduler_type == 'none':
        return None
    
    elif scheduler_type == 'cosine_warmup':
        return CosineWarmupScheduler(
            optimizer,
            warmup_steps=warmup_steps,
            total_steps=total_steps,
            min_lr_ratio=min_lr_ratio
        )
    
    elif scheduler_type == 'linear_warmup':
        def lr_lambda(step):
            if step < warmup_steps:
                return step / warmup_steps
            else:
                return max(min_lr_ratio, 1.0 - (step - warmup_steps) / (total_steps - warmup_steps))
        
        return optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)
    
    elif scheduler_type == 'cosine':
        return optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=total_steps,
            eta_min=optimizer.param_groups[0]['lr'] * min_lr_ratio
        )
    
    else:
        raise ValueError(f"Unknown scheduler type: {scheduler_type}")


def get_parameter_count(model: nn.Module, trainable_only: bool = True) -> int:
    """Get the number of parameters in the model."""
    if trainable_only:
        return sum(p.numel() for p in model.parameters() if p.requires_grad)
    else:
        return sum(p.numel() for p in model.parameters())


def freeze_parameters(model: nn.Module, layer_patterns: List[str]):
    """Freeze specific layers in the model."""
    frozen_count = 0
    for name, param in model.named_parameters():
        if any(pattern in name for pattern in layer_patterns):
            param.requires_grad = False
            frozen_count += 1
    
    print(f"🧊 Frozen {frozen_count} parameters")


def unfreeze_parameters(model: nn.Module, layer_patterns: List[str]):
    """Unfreeze specific layers in the model."""
    unfrozen_count = 0
    for name, param in model.named_parameters():
        if any(pattern in name for pattern in layer_patterns):
            param.requires_grad = True
            unfrozen_count += 1
    
    print(f"🔥 Unfrozen {unfrozen_count} parameters")


def get_learning_rate(optimizer: torch.optim.Optimizer) -> float:
    """Get current learning rate from optimizer."""
    return optimizer.param_groups[0]['lr']


def set_learning_rate(optimizer: torch.optim.Optimizer, lr: float):
    """Set learning rate for all parameter groups."""
    for param_group in optimizer.param_groups:
        param_group['lr'] = lr


def calculate_model_flops(model: nn.Module, input_shape: tuple) -> int:
    """
    Estimate FLOPs for the model (rough approximation).
    
    Args:
        model: Model to analyze
        input_shape: Input tensor shape (batch_size, seq_len)
        
    Returns:
        Estimated FLOPs
    """
    batch_size, seq_len = input_shape
    
    # Get model parameters
    total_params = get_parameter_count(model)
    
    # Rough FLOP estimation for transformer
    # Forward pass: ~6 * params * batch_size * seq_len
    # (This is a very rough approximation)
    forward_flops = 6 * total_params * batch_size * seq_len
    
    return forward_flops
