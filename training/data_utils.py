"""
Featureimanbug AI - Data Loading and Processing Utilities

This module provides comprehensive data handling for training the Featureimanbug AI model,
including support for various text datasets and efficient preprocessing.
"""
import torch
from torch.utils.data import Dataset, DataLoader
import tiktoken
import numpy as np
from typing import List, Optional, Union, Dict, Any, Tuple
import os
import json
import requests
from pathlib import Path
import gzip
import pickle

# Hugging Face datasets for easy access to training data
try:
    from datasets import load_dataset, Dataset as HFDataset
    HF_DATASETS_AVAILABLE = True
except ImportError:
    HF_DATASETS_AVAILABLE = False
    print("⚠️  Hugging Face datasets not available. Install with: pip install datasets")


class TextDataset(Dataset):
    """
    Efficient text dataset for language model training.
    
    Supports both character-level and token-level processing with sliding windows.
    """
    
    def __init__(self, 
                 text_data: Union[str, List[str]], 
                 tokenizer: tiktoken.Encoding,
                 max_length: int = 1024,
                 stride: int = 512,
                 add_special_tokens: bool = True):
        """
        Initialize the dataset.
        
        Args:
            text_data: Raw text string or list of text strings
            tokenizer: Tokenizer for encoding text
            max_length: Maximum sequence length
            stride: Stride for sliding window
            add_special_tokens: Whether to add special tokens
        """
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.stride = stride
        
        # Process text data
        if isinstance(text_data, str):
            text_data = [text_data]
        
        # Combine all text
        combined_text = "\n\n".join(text_data)
        
        # Add special tokens if requested
        if add_special_tokens:
            combined_text = "<|startoftext|>" + combined_text + "<|endoftext|>"
        
        # Tokenize
        print("🔤 Tokenizing text...")
        self.tokens = tokenizer.encode(combined_text)
        print(f"📊 Total tokens: {len(self.tokens):,}")
        
        # Create sliding windows
        self.input_ids = []
        self.target_ids = []
        
        for i in range(0, len(self.tokens) - max_length, stride):
            input_chunk = self.tokens[i:i + max_length]
            target_chunk = self.tokens[i + 1:i + max_length + 1]
            
            if len(input_chunk) == max_length and len(target_chunk) == max_length:
                self.input_ids.append(input_chunk)
                self.target_ids.append(target_chunk)
        
        print(f"📦 Created {len(self.input_ids)} training sequences")
    
    def __len__(self) -> int:
        return len(self.input_ids)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        return (
            torch.tensor(self.input_ids[idx], dtype=torch.long),
            torch.tensor(self.target_ids[idx], dtype=torch.long)
        )


class WebTextDataset:
    """Utility class for downloading and preparing web-based text datasets."""
    
    @staticmethod
    def download_openwebtext(cache_dir: str = "/content/data") -> str:
        """Download OpenWebText dataset (subset)."""
        if not HF_DATASETS_AVAILABLE:
            raise ImportError("Hugging Face datasets required for OpenWebText")
        
        print("📥 Downloading OpenWebText dataset...")
        dataset = load_dataset("openwebtext", split="train", streaming=True)
        
        # Take a subset for Colab (adjust size as needed)
        texts = []
        for i, example in enumerate(dataset):
            if i >= 10000:  # Limit for Colab memory
                break
            texts.append(example['text'])
            
            if i % 1000 == 0:
                print(f"   Downloaded {i} examples...")
        
        combined_text = "\n\n".join(texts)
        
        # Save to cache
        os.makedirs(cache_dir, exist_ok=True)
        cache_path = os.path.join(cache_dir, "openwebtext_subset.txt")
        with open(cache_path, 'w', encoding='utf-8') as f:
            f.write(combined_text)
        
        print(f"💾 Cached dataset: {cache_path}")
        return combined_text
    
    @staticmethod
    def download_wikitext(version: str = "wikitext-103-v1", cache_dir: str = "/content/data") -> str:
        """Download WikiText dataset."""
        if not HF_DATASETS_AVAILABLE:
            raise ImportError("Hugging Face datasets required for WikiText")
        
        print(f"📥 Downloading {version} dataset...")
        dataset = load_dataset("wikitext", version, split="train")
        
        # Combine all text
        texts = [example['text'] for example in dataset if example['text'].strip()]
        combined_text = "\n\n".join(texts)
        
        # Save to cache
        os.makedirs(cache_dir, exist_ok=True)
        cache_path = os.path.join(cache_dir, f"{version}.txt")
        with open(cache_path, 'w', encoding='utf-8') as f:
            f.write(combined_text)
        
        print(f"💾 Cached dataset: {cache_path}")
        return combined_text
    
    @staticmethod
    def load_custom_text(file_paths: List[str]) -> str:
        """Load custom text files."""
        texts = []
        
        for file_path in file_paths:
            print(f"📖 Loading: {file_path}")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    text = f.read()
                    texts.append(text)
                    print(f"   Loaded {len(text):,} characters")
            except Exception as e:
                print(f"   ❌ Error loading {file_path}: {e}")
        
        return "\n\n".join(texts)


def create_dataloader(dataset: Dataset,
                     batch_size: int = 8,
                     shuffle: bool = True,
                     num_workers: int = 2,
                     pin_memory: bool = True,
                     drop_last: bool = True) -> DataLoader:
    """
    Create an optimized DataLoader for training.
    
    Args:
        dataset: Dataset instance
        batch_size: Batch size (optimized for Colab)
        shuffle: Whether to shuffle data
        num_workers: Number of worker processes (limited for Colab)
        pin_memory: Whether to pin memory for faster GPU transfer
        drop_last: Whether to drop last incomplete batch
        
    Returns:
        DataLoader instance
    """
    return DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        pin_memory=pin_memory,
        drop_last=drop_last,
        persistent_workers=num_workers > 0
    )


def prepare_training_data(data_source: str,
                         tokenizer: tiktoken.Encoding,
                         max_length: int = 1024,
                         stride: int = 512,
                         train_split: float = 0.9,
                         cache_dir: str = "/content/data") -> Tuple[Dataset, Optional[Dataset]]:
    """
    Prepare training and validation datasets from various sources.
    
    Args:
        data_source: Data source specification
        tokenizer: Tokenizer instance
        max_length: Maximum sequence length
        stride: Stride for sliding window
        train_split: Fraction of data for training
        cache_dir: Directory for caching data
        
    Returns:
        Tuple of (train_dataset, val_dataset)
    """
    print(f"🔧 Preparing data from: {data_source}")
    
    # Load text based on source
    if data_source == "openwebtext":
        text_data = WebTextDataset.download_openwebtext(cache_dir)
    elif data_source == "wikitext":
        text_data = WebTextDataset.download_wikitext(cache_dir=cache_dir)
    elif data_source.startswith("file:"):
        # Custom file(s)
        file_paths = data_source[5:].split(",")
        text_data = WebTextDataset.load_custom_text(file_paths)
    elif os.path.exists(data_source):
        # Single file
        with open(data_source, 'r', encoding='utf-8') as f:
            text_data = f.read()
    else:
        # Treat as raw text
        text_data = data_source
    
    print(f"📊 Total text length: {len(text_data):,} characters")
    
    # Split into train/validation
    if train_split < 1.0:
        split_idx = int(len(text_data) * train_split)
        train_text = text_data[:split_idx]
        val_text = text_data[split_idx:]
        
        print(f"📚 Train text: {len(train_text):,} characters")
        print(f"📖 Val text: {len(val_text):,} characters")
        
        # Create datasets
        train_dataset = TextDataset(train_text, tokenizer, max_length, stride)
        val_dataset = TextDataset(val_text, tokenizer, max_length, stride)
        
        return train_dataset, val_dataset
    else:
        # Only training data
        train_dataset = TextDataset(text_data, tokenizer, max_length, stride)
        return train_dataset, None


def estimate_memory_usage(batch_size: int, max_length: int, vocab_size: int, 
                         emb_dim: int, n_layers: int) -> Dict[str, float]:
    """
    Estimate memory usage for training.
    
    Returns memory estimates in GB.
    """
    # Model parameters
    model_params = (
        vocab_size * emb_dim +  # Token embeddings
        max_length * emb_dim +  # Position embeddings
        n_layers * (
            3 * emb_dim * emb_dim +  # QKV projections
            emb_dim * emb_dim +      # Output projection
            4 * emb_dim * emb_dim +  # Feed-forward
            4 * emb_dim              # Layer norms
        ) +
        vocab_size * emb_dim  # Output head
    )
    
    # Memory estimates (rough)
    model_memory = model_params * 4 / 1e9  # 4 bytes per float32
    
    # Activations (forward pass)
    activation_memory = batch_size * max_length * emb_dim * n_layers * 4 / 1e9
    
    # Gradients (same size as model)
    gradient_memory = model_memory
    
    # Optimizer states (AdamW: 2x model size)
    optimizer_memory = model_memory * 2
    
    total_memory = model_memory + activation_memory + gradient_memory + optimizer_memory
    
    return {
        'model_memory': model_memory,
        'activation_memory': activation_memory,
        'gradient_memory': gradient_memory,
        'optimizer_memory': optimizer_memory,
        'total_memory': total_memory
    }


def optimize_batch_size_for_colab(max_length: int = 1024, 
                                 emb_dim: int = 768, 
                                 n_layers: int = 12,
                                 available_memory_gb: float = 12.0) -> int:
    """
    Automatically determine optimal batch size for Colab.
    
    Args:
        max_length: Sequence length
        emb_dim: Embedding dimension
        n_layers: Number of layers
        available_memory_gb: Available GPU memory in GB
        
    Returns:
        Recommended batch size
    """
    vocab_size = 50257  # GPT-2 vocab size
    
    # Try different batch sizes
    for batch_size in [32, 16, 8, 4, 2, 1]:
        memory_usage = estimate_memory_usage(
            batch_size, max_length, vocab_size, emb_dim, n_layers
        )
        
        if memory_usage['total_memory'] < available_memory_gb * 0.8:  # 80% safety margin
            print(f"🎯 Recommended batch size: {batch_size}")
            print(f"   Estimated memory usage: {memory_usage['total_memory']:.1f} GB")
            return batch_size
    
    print("⚠️  Warning: Even batch size 1 might be too large!")
    return 1


# Sample datasets for quick testing
SAMPLE_TEXTS = {
    "shakespeare": """
To be, or not to be, that is the question:
Whether 'tis nobler in the mind to suffer
The slings and arrows of outrageous fortune,
Or to take arms against a sea of troubles
And by opposing end them.
""" * 100,  # Repeat for more data
    
    "science": """
Artificial intelligence represents one of the most significant technological 
advances of our time. Machine learning algorithms can now process vast amounts 
of data, recognize patterns, and make predictions with remarkable accuracy. 
Deep learning, a subset of machine learning, uses neural networks with multiple 
layers to model and understand complex patterns in data.
""" * 100,
    
    "conversation": """
Hello, how are you today? I'm doing well, thank you for asking. 
What would you like to talk about? I'm interested in discussing 
technology and artificial intelligence. That sounds fascinating! 
Can you tell me more about how AI works? Certainly! AI systems 
learn from data to make predictions and decisions.
""" * 100
}
