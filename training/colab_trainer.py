"""
Featureimanbug AI - Google Colab Training Pipeline

This module provides a complete training pipeline optimized for Google Colab,
including checkpointing, monitoring, and memory optimization.
"""
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import os
import json
import time
import math
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import asdict
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm.auto import tqdm
import gc

# Weights & Biases for experiment tracking
try:
    import wandb
    WANDB_AVAILABLE = True
except ImportError:
    WANDB_AVAILABLE = False
    print("⚠️  Weights & Biases not available. Install with: pip install wandb")

import sys
import os
sys.path.append('/content/AIbYME/src')  # Colab path

from model.featureimanbug_model import FeatureimanbugModel, FeatureimanbugConfig
from training.data_utils import create_dataloader, TextDataset
from training.optimization import create_optimizer, create_scheduler, GradientClipper


class ColabTrainer:
    """
    Featureimanbug AI trainer optimized for Google Colab.
    
    Features:
    - Automatic checkpointing for session recovery
    - Memory optimization for Colab's GPU limits
    - Comprehensive logging and visualization
    - Real-time monitoring and sample generation
    - Integration with Weights & Biases
    """
    
    def __init__(self, 
                 config: FeatureimanbugConfig,
                 model_name: str = "featureimanbug-ai",
                 checkpoint_dir: str = "/content/drive/MyDrive/featureimanbug_checkpoints",
                 use_wandb: bool = True,
                 wandb_project: str = "featureimanbug-ai"):
        
        self.config = config
        self.model_name = model_name
        self.checkpoint_dir = checkpoint_dir
        self.use_wandb = use_wandb and WANDB_AVAILABLE
        
        # Create checkpoint directory
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        # Initialize device
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🖥️  Using device: {self.device}")
        
        if torch.cuda.is_available():
            print(f"📊 GPU: {torch.cuda.get_device_name()}")
            print(f"💾 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        
        # Initialize model
        self.model = FeatureimanbugModel(config).to(self.device)
        print(f"🧠 Model parameters: {self.model.get_num_params():,}")
        
        # Initialize optimizer and scheduler
        self.optimizer = create_optimizer(self.model, config.learning_rate, config.weight_decay)
        self.scheduler = None  # Will be set when training starts
        self.grad_clipper = GradientClipper(max_norm=1.0)
        
        # Training state
        self.current_epoch = 0
        self.current_step = 0
        self.best_loss = float('inf')
        self.training_losses = []
        self.validation_losses = []
        self.learning_rates = []
        
        # Initialize Weights & Biases
        if self.use_wandb:
            wandb.init(
                project=wandb_project,
                name=f"{model_name}-{int(time.time())}",
                config=asdict(config)
            )
            wandb.watch(self.model, log_freq=100)
    
    def save_checkpoint(self, epoch: int, loss: float, is_best: bool = False):
        """Save training checkpoint."""
        checkpoint = {
            'epoch': epoch,
            'step': self.current_step,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'loss': loss,
            'best_loss': self.best_loss,
            'config': asdict(self.config),
            'training_losses': self.training_losses,
            'validation_losses': self.validation_losses,
            'learning_rates': self.learning_rates
        }
        
        # Save regular checkpoint
        checkpoint_path = os.path.join(self.checkpoint_dir, f"{self.model_name}_epoch_{epoch}.pt")
        torch.save(checkpoint, checkpoint_path)
        
        # Save best model
        if is_best:
            best_path = os.path.join(self.checkpoint_dir, f"{self.model_name}_best.pt")
            torch.save(checkpoint, best_path)
            print(f"💾 New best model saved: {best_path}")
        
        # Save latest checkpoint
        latest_path = os.path.join(self.checkpoint_dir, f"{self.model_name}_latest.pt")
        torch.save(checkpoint, latest_path)
        
        print(f"💾 Checkpoint saved: {checkpoint_path}")
    
    def load_checkpoint(self, checkpoint_path: str) -> bool:
        """Load training checkpoint."""
        try:
            checkpoint = torch.load(checkpoint_path, map_location=self.device)
            
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            
            if checkpoint.get('scheduler_state_dict') and self.scheduler:
                self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            
            self.current_epoch = checkpoint['epoch']
            self.current_step = checkpoint['step']
            self.best_loss = checkpoint['best_loss']
            self.training_losses = checkpoint.get('training_losses', [])
            self.validation_losses = checkpoint.get('validation_losses', [])
            self.learning_rates = checkpoint.get('learning_rates', [])
            
            print(f"✅ Checkpoint loaded: {checkpoint_path}")
            print(f"   Epoch: {self.current_epoch}, Step: {self.current_step}")
            print(f"   Best loss: {self.best_loss:.4f}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to load checkpoint: {e}")
            return False
    
    def find_latest_checkpoint(self) -> Optional[str]:
        """Find the latest checkpoint in the checkpoint directory."""
        latest_path = os.path.join(self.checkpoint_dir, f"{self.model_name}_latest.pt")
        if os.path.exists(latest_path):
            return latest_path
        return None
    
    def optimize_memory(self):
        """Optimize memory usage for Colab."""
        # Clear cache
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # Garbage collection
        gc.collect()
        
        # Set memory fraction (use 90% of available GPU memory)
        if torch.cuda.is_available():
            torch.cuda.set_per_process_memory_fraction(0.9)
    
    def generate_sample(self, prompt: str = "The future of artificial intelligence", 
                       max_tokens: int = 50) -> str:
        """Generate a sample text for monitoring training progress."""
        self.model.eval()
        
        # Simple tokenization (you might want to use a proper tokenizer)
        # For now, we'll use character-level tokenization as a placeholder
        tokens = [ord(c) for c in prompt]
        input_ids = torch.tensor([tokens], device=self.device)
        
        with torch.no_grad():
            generated = self.model.generate(
                input_ids, 
                max_new_tokens=max_tokens,
                temperature=self.config.temperature,
                top_k=self.config.top_k,
                top_p=self.config.top_p
            )
        
        # Convert back to text (character-level)
        generated_text = ''.join([chr(min(max(t.item(), 32), 126)) for t in generated[0]])
        
        self.model.train()
        return generated_text
    
    def plot_training_progress(self):
        """Plot training progress."""
        if not self.training_losses:
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Training loss
        axes[0, 0].plot(self.training_losses)
        axes[0, 0].set_title('Training Loss')
        axes[0, 0].set_xlabel('Step')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].grid(True)
        
        # Validation loss
        if self.validation_losses:
            axes[0, 1].plot(self.validation_losses)
            axes[0, 1].set_title('Validation Loss')
            axes[0, 1].set_xlabel('Epoch')
            axes[0, 1].set_ylabel('Loss')
            axes[0, 1].grid(True)
        
        # Learning rate
        if self.learning_rates:
            axes[1, 0].plot(self.learning_rates)
            axes[1, 0].set_title('Learning Rate')
            axes[1, 0].set_xlabel('Step')
            axes[1, 0].set_ylabel('LR')
            axes[1, 0].grid(True)
        
        # GPU memory usage (if available)
        if torch.cuda.is_available():
            memory_used = torch.cuda.memory_allocated() / 1e9
            memory_total = torch.cuda.get_device_properties(0).total_memory / 1e9
            
            axes[1, 1].bar(['Used', 'Available'], 
                          [memory_used, memory_total - memory_used],
                          color=['red', 'green'])
            axes[1, 1].set_title('GPU Memory Usage (GB)')
            axes[1, 1].set_ylabel('Memory (GB)')
        
        plt.tight_layout()
        plt.show()
        
        # Save plot
        plot_path = os.path.join(self.checkpoint_dir, 'training_progress.png')
        plt.savefig(plot_path, dpi=150, bbox_inches='tight')
        
        if self.use_wandb:
            wandb.log({"training_progress": wandb.Image(plot_path)})
    
    def log_metrics(self, metrics: Dict[str, Any], step: Optional[int] = None):
        """Log metrics to console and Weights & Biases."""
        step = step or self.current_step
        
        # Console logging
        log_str = f"Step {step:6d} | "
        for key, value in metrics.items():
            if isinstance(value, float):
                log_str += f"{key}: {value:.4f} | "
            else:
                log_str += f"{key}: {value} | "
        print(log_str)
        
        # Weights & Biases logging
        if self.use_wandb:
            wandb.log(metrics, step=step)
    
    def estimate_time_remaining(self, epoch: int, total_epochs: int, 
                              epoch_start_time: float) -> str:
        """Estimate remaining training time."""
        if epoch == 0:
            return "Unknown"
        
        elapsed_time = time.time() - epoch_start_time
        avg_time_per_epoch = elapsed_time / (epoch + 1)
        remaining_epochs = total_epochs - epoch - 1
        remaining_time = avg_time_per_epoch * remaining_epochs
        
        hours = int(remaining_time // 3600)
        minutes = int((remaining_time % 3600) // 60)
        
        return f"{hours}h {minutes}m"

    def train_epoch(self, train_dataloader: DataLoader, epoch: int) -> float:
        """Train for one epoch."""
        self.model.train()
        total_loss = 0.0
        num_batches = len(train_dataloader)

        # Progress bar
        pbar = tqdm(train_dataloader, desc=f"Epoch {epoch+1}")

        for batch_idx, (input_ids, targets) in enumerate(pbar):
            # Move to device
            input_ids = input_ids.to(self.device)
            targets = targets.to(self.device)

            # Forward pass
            logits, loss = self.model(input_ids, targets)

            # Backward pass
            self.optimizer.zero_grad()
            loss.backward()

            # Gradient clipping
            grad_norm = self.grad_clipper.clip_gradients(self.model)

            # Optimizer step
            self.optimizer.step()

            # Scheduler step
            if self.scheduler:
                self.scheduler.step()
                current_lr = self.scheduler.get_last_lr()[0]
            else:
                current_lr = self.optimizer.param_groups[0]['lr']

            # Update metrics
            total_loss += loss.item()
            self.training_losses.append(loss.item())
            self.learning_rates.append(current_lr)
            self.current_step += 1

            # Update progress bar
            pbar.set_postfix({
                'loss': f"{loss.item():.4f}",
                'avg_loss': f"{total_loss / (batch_idx + 1):.4f}",
                'lr': f"{current_lr:.2e}",
                'grad_norm': f"{grad_norm:.3f}"
            })

            # Log metrics periodically
            if self.current_step % 50 == 0:
                metrics = {
                    'train_loss': loss.item(),
                    'learning_rate': current_lr,
                    'gradient_norm': grad_norm,
                    'epoch': epoch
                }
                self.log_metrics(metrics)

            # Generate sample periodically
            if self.current_step % 500 == 0:
                sample_text = self.generate_sample()
                print(f"\n📝 Sample generation: {sample_text}\n")

                if self.use_wandb:
                    wandb.log({"sample_generation": sample_text}, step=self.current_step)

            # Memory optimization
            if batch_idx % 100 == 0:
                self.optimize_memory()

        avg_loss = total_loss / num_batches
        return avg_loss

    def validate(self, val_dataloader: DataLoader) -> float:
        """Validate the model."""
        self.model.eval()
        total_loss = 0.0
        num_batches = len(val_dataloader)

        with torch.no_grad():
            for input_ids, targets in tqdm(val_dataloader, desc="Validation"):
                input_ids = input_ids.to(self.device)
                targets = targets.to(self.device)

                logits, loss = self.model(input_ids, targets)
                total_loss += loss.item()

        avg_loss = total_loss / num_batches
        self.validation_losses.append(avg_loss)

        return avg_loss

    def train(self, train_dataloader: DataLoader,
              val_dataloader: Optional[DataLoader] = None,
              resume_from_checkpoint: bool = True):
        """Main training loop."""
        print("🚀 Starting Featureimanbug AI Training")
        print("=" * 60)

        # Try to resume from checkpoint
        if resume_from_checkpoint:
            latest_checkpoint = self.find_latest_checkpoint()
            if latest_checkpoint:
                self.load_checkpoint(latest_checkpoint)
                print(f"🔄 Resuming from epoch {self.current_epoch}")

        # Initialize scheduler
        total_steps = len(train_dataloader) * self.config.max_epochs
        self.scheduler = create_scheduler(
            self.optimizer,
            warmup_steps=self.config.warmup_steps,
            total_steps=total_steps
        )

        # Training loop
        training_start_time = time.time()

        for epoch in range(self.current_epoch, self.config.max_epochs):
            epoch_start_time = time.time()

            print(f"\n📚 Epoch {epoch + 1}/{self.config.max_epochs}")
            print("-" * 40)

            # Train
            train_loss = self.train_epoch(train_dataloader, epoch)

            # Validate
            val_loss = None
            if val_dataloader:
                val_loss = self.validate(val_dataloader)
                print(f"📊 Validation Loss: {val_loss:.4f}")

                # Check if this is the best model
                is_best = val_loss < self.best_loss
                if is_best:
                    self.best_loss = val_loss
                    print("🏆 New best model!")
            else:
                is_best = train_loss < self.best_loss
                if is_best:
                    self.best_loss = train_loss

            # Log epoch metrics
            epoch_time = time.time() - epoch_start_time
            remaining_time = self.estimate_time_remaining(
                epoch, self.config.max_epochs, training_start_time
            )

            epoch_metrics = {
                'epoch': epoch + 1,
                'train_loss': train_loss,
                'epoch_time': epoch_time,
                'remaining_time': remaining_time
            }

            if val_loss is not None:
                epoch_metrics['val_loss'] = val_loss

            self.log_metrics(epoch_metrics)

            # Save checkpoint
            self.current_epoch = epoch + 1
            self.save_checkpoint(epoch + 1, val_loss or train_loss, is_best)

            # Plot progress
            if (epoch + 1) % 5 == 0:  # Plot every 5 epochs
                self.plot_training_progress()

            # Generate sample
            sample_text = self.generate_sample()
            print(f"📝 Sample: {sample_text}")

            print(f"⏱️  Epoch time: {epoch_time:.1f}s | Remaining: {remaining_time}")

        total_time = time.time() - training_start_time
        print(f"\n🎉 Training completed in {total_time/3600:.1f} hours!")

        # Final plot
        self.plot_training_progress()

        # Save final model
        final_path = os.path.join(self.checkpoint_dir, f"{self.model_name}_final.pt")
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'config': asdict(self.config),
            'training_losses': self.training_losses,
            'validation_losses': self.validation_losses
        }, final_path)

        print(f"💾 Final model saved: {final_path}")

        if self.use_wandb:
            wandb.finish()
