# 🤖 Featureimanbug AI

A comprehensive GPT-like language model implementation built from scratch with advanced features and a ChatGPT-like interface.

## ✨ Features

### 🌐 **Web-Based Chat Interface**
- **Modern web UI** similar to ChatGPT
- **Real-time streaming** responses
- **Conversation management** (create, switch, delete)
- **Persistent chat history** with search
- **Customizable settings** (temperature, sampling, etc.)
- **Mobile-responsive** design
- **Export conversations** (JSON/TXT formats)

### 🧠 **Model Architecture**
- **Transformer-based GPT model** with multi-head attention
- **GPT-2 355M integration** with weight conversion
- **Configurable model sizes** (Small, 124M, 355M parameters)
- **Advanced attention mechanisms** with causal masking
- **Layer normalization** and **GELU activations**
- **Proper weight initialization** for stable training

### 🎯 **Advanced Text Generation**
- **Multiple sampling strategies**:
  - Greedy decoding
  - Temperature sampling
  - Top-k sampling
  - Top-p (nucleus) sampling
  - Beam search
- **Repetition penalty** and **stop tokens**
- **Streaming generation** for real-time responses

### 💬 **Multiple Interfaces**
- **Web interface** (recommended) - ChatGPT-like experience
- **Command-line chat** - Terminal-based interaction
- **API endpoints** - For integration with other applications
- **Conversation history** with persistent storage
- **Context-aware responses** with memory

### 🚀 **Training & Optimization**
- **Advanced optimizer** with weight decay handling
- **Learning rate scheduling** with warmup
- **Gradient clipping** for stable training
- **Exponential moving average** (EMA) support
- **Comprehensive logging** and checkpointing
- **Model size estimation** and parameter counting

## 📦 Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd AIbYME
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Verify installation:**
```bash
python main.py --help
```

## 🚀 Quick Start

### 🌐 **Web Interface (Recommended)**

The easiest way to use Featureimanbug AI with your GPT-2 355M model:

```bash
# Easy startup (handles everything automatically)
python start_web_interface.py

# Or use the main script
python main.py web
```

Then open **http://localhost:5000** in your browser for a ChatGPT-like interface!

### 🔄 **Model Conversion**

Convert your GPT-2 355M model for optimal performance:

```bash
# Convert TensorFlow GPT-2 to PyTorch format
python main.py convert
```

### 💬 **Command-Line Chat**

For terminal-based interaction:

```bash
# Use converted model
python main.py chat --model-path models/gpt2_355m_converted.pt

# Use original GPT-2 model
python main.py chat --model-path gpt2/355M
```

### ✨ **Text Generation**

Generate text from prompts:

```bash
# Basic generation
python main.py generate --prompt "The future of AI is" --model-path models/gpt2_355m_converted.pt

# Advanced generation with custom parameters
python main.py generate \
  --prompt "Once upon a time" \
  --model-path models/gpt2_355m_converted.pt \
  --max-tokens 200 \
  --strategy top_p \
  --temperature 0.8 \
  --top-p 0.9
```

### 🎓 **Training Custom Models**

Train your own models on custom data:

```bash
# Train a small model
python main.py train --data-path your_data.txt --model-size small

# Train a larger model
python main.py train --data-path your_data.txt --model-size 124m

# Resume training from checkpoint
python main.py train --data-path your_data.txt --resume checkpoints/checkpoint.pt
```

## 📁 Project Structure

```
AIbYME/
├── src/                          # Source code
│   ├── model/                    # Model architecture
│   │   ├── gpt.py               # Main GPT model
│   │   ├── attention.py         # Attention mechanisms
│   │   └── layers.py            # Neural network layers
│   ├── generation/               # Text generation
│   │   ├── generator.py         # Main generator class
│   │   └── strategies.py        # Sampling strategies
│   ├── chat/                     # Chat interface
│   │   ├── interface.py         # Main chat interface
│   │   ├── conversation.py      # Conversation management
│   │   ├── history.py           # History management
│   │   └── cli.py               # Command-line interface
│   ├── training/                 # Training utilities
│   │   ├── trainer.py           # Main trainer class
│   │   ├── dataset.py           # Dataset utilities
│   │   ├── optimizer.py         # Optimizer utilities
│   │   └── utils.py             # Training utilities
│   └── config.py                # Configuration management
├── main.py                       # Main application entry point
├── requirements.txt              # Python dependencies
└── README.md                     # This file
```

## ⚙️ Configuration

### Model Configurations

The project includes several predefined configurations:

- **Small** (6 layers, 384 dim): Fast training, good for testing
- **124M** (12 layers, 768 dim): Balanced performance and size
- **355M** (24 layers, 1024 dim): High performance, requires more resources

### Custom Configuration

Create a custom configuration file:

```json
{
  "vocab_size": 50257,
  "context_length": 1024,
  "emb_dim": 768,
  "n_heads": 12,
  "n_layers": 12,
  "drop_rate": 0.1,
  "qkv_bias": false,
  "learning_rate": 0.0003,
  "batch_size": 8,
  "max_epochs": 10,
  "warmup_steps": 1000,
  "weight_decay": 0.01
}
```

## 💬 Chat Interface Commands

The chat interface supports various commands:

- `/help` - Show help message
- `/new` - Start a new conversation
- `/list` - List all conversations
- `/switch` - Switch to a different conversation
- `/delete` - Delete a conversation
- `/search` - Search conversations
- `/settings` - Show/update generation settings
- `/export` - Export conversations
- `/stats` - Show chat statistics
- `/quit` - Exit the chat

## 🎛️ Generation Parameters

### Sampling Strategies

1. **Greedy**: Always select the most likely token
2. **Temperature**: Control randomness (higher = more random)
3. **Top-k**: Sample from k most likely tokens
4. **Top-p**: Sample from tokens with cumulative probability ≤ p
5. **Beam Search**: Find high-probability sequences

### Parameters

- `temperature`: Controls randomness (0.1-2.0, default: 0.8)
- `top_k`: Number of top tokens to consider (1-100, default: 50)
- `top_p`: Cumulative probability threshold (0.1-1.0, default: 0.9)
- `repetition_penalty`: Penalty for repeating tokens (1.0-2.0, default: 1.1)
- `max_new_tokens`: Maximum tokens to generate (1-1000, default: 100)

## 🔧 Advanced Usage

### Training with Custom Data

```python
from src.training.dataset import prepare_dataset_from_directory
from src.training.trainer import GPTTrainer
from src.model.gpt import GPTModel
from src.config import GPTConfig

# Load configuration
config = GPTConfig.load("config.json")

# Create model
model = GPTModel(config)

# Prepare dataset from directory
dataset = prepare_dataset_from_directory(
    data_dir="data/",
    tokenizer=tokenizer,
    max_length=config.context_length
)

# Create trainer
trainer = GPTTrainer(model, config)

# Train
for epoch in range(config.max_epochs):
    trainer.train_epoch(dataloader, epoch)
```

### Custom Generation

```python
from src.generation.generator import TextGenerator
from src.generation.strategies import TopPSampling

# Create custom strategy
strategy = TopPSampling(p=0.95, temperature=0.7)

# Generate with custom strategy
generator = TextGenerator(model, tokenizer)
text = generator.generate(
    prompt="Your prompt here",
    strategy="top_p",
    top_p=0.95,
    temperature=0.7
)
```

## 📊 Performance Tips

### Training
- Use **gradient accumulation** for larger effective batch sizes
- Enable **mixed precision** training for faster training on modern GPUs
- Use **learning rate scheduling** for better convergence
- Monitor **gradient norms** to detect training instabilities

### Inference
- Use **torch.compile()** for faster inference (PyTorch 2.0+)
- Enable **KV-cache** for faster sequential generation
- Use **quantization** for reduced memory usage
- Batch multiple requests for better throughput

## 🤝 Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.

## 📄 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- Inspired by the original GPT papers and implementations
- Built with PyTorch and modern deep learning best practices
- Thanks to the open source community for tools and libraries

---

**Featureimanbug AI** - Building the future of conversational AI, one token at a time! 🚀
