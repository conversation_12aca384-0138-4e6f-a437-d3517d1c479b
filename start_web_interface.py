#!/usr/bin/env python3
"""
Featureimanbug AI - Easy Web Interface Starter

This script provides an easy way to start the web interface with automatic
model detection and conversion.
"""
import os
import sys
import subprocess
import time
from pathlib import Path


def check_dependencies():
    """Check if required dependencies are installed."""
    try:
        import flask
        import flask_socketio
        import torch
        import tiktoken
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("📦 Installing dependencies...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], check=True)
            print("✅ Dependencies installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies")
            return False


def check_model_status():
    """Check model status and return path and type."""
    converted_path = "models/gpt2_355m_converted.pt"
    gpt2_path = "gpt2/355M"
    
    if os.path.exists(converted_path):
        return converted_path, "converted", True
    elif os.path.exists(gpt2_path):
        return gpt2_path, "tensorflow", False
    else:
        return None, None, False


def convert_model():
    """Convert GPT-2 model to PyTorch format."""
    print("🔄 Converting GPT-2 model to PyTorch format...")
    print("   This may take a few minutes...")
    
    try:
        result = subprocess.run([sys.executable, 'scripts/convert_gpt2.py'], 
                              capture_output=True, text=True, check=True)
        print("✅ Model conversion completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Model conversion failed: {e}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False


def start_web_server():
    """Start the web server."""
    print("🚀 Starting Featureimanbug AI Web Interface...")
    print("   Please wait while the model loads...")
    
    try:
        # Start web server
        subprocess.run([sys.executable, 'web_server.py'], check=True)
    except KeyboardInterrupt:
        print("\n👋 Web interface stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Web server failed: {e}")


def main():
    """Main startup function."""
    print("🤖 Featureimanbug AI - Web Interface Starter")
    print("=" * 50)
    
    # Check dependencies
    print("📦 Checking dependencies...")
    if not check_dependencies():
        print("❌ Please install dependencies manually:")
        print("   pip install -r requirements.txt")
        return
    
    print("✅ Dependencies OK")
    
    # Check model status
    print("\n🔍 Checking model status...")
    model_path, model_type, ready = check_model_status()
    
    if not model_path:
        print("❌ No GPT-2 model found!")
        print("\nPlease ensure you have the GPT-2 355M model in one of these locations:")
        print("   - gpt2/355M/ (TensorFlow format)")
        print("   - models/gpt2_355m_converted.pt (converted PyTorch format)")
        print("\nYou can download the GPT-2 model using the original download script.")
        return
    
    if model_type == "tensorflow":
        print(f"📂 Found TensorFlow model: {model_path}")
        print("🔄 Conversion to PyTorch format required for optimal performance")
        
        response = input("Convert model now? (Y/n): ").strip().lower()
        if response in ['', 'y', 'yes']:
            if not convert_model():
                print("⚠️  Conversion failed, but we can still try to use the TensorFlow model")
                print("   (This will be slower and require TensorFlow)")
        else:
            print("⚠️  Using TensorFlow model (slower loading)")
    
    elif model_type == "converted":
        print(f"✅ Found converted PyTorch model: {model_path}")
    
    # Final check
    model_path, model_type, ready = check_model_status()
    
    print(f"\n🎯 Using model: {model_path}")
    print(f"📊 Model type: {model_type}")
    
    # Start web interface
    print("\n" + "=" * 50)
    print("🌐 Starting Web Interface")
    print("=" * 50)
    print("📍 URL: http://localhost:5000")
    print("🔧 Use Ctrl+C to stop the server")
    print("=" * 50)
    
    # Give user a moment to read
    time.sleep(2)
    
    # Start server
    start_web_server()


if __name__ == "__main__":
    main()
