{"cells": [{"cell_type": "markdown", "id": "073ad29d", "metadata": {}, "source": ["# 4 Implementing a GPT model from scratch to generate text"]}, {"cell_type": "markdown", "id": "e3dd5721", "metadata": {}, "source": ["## 4.1 Coding an LLM architecuture"]}, {"cell_type": "code", "execution_count": 1, "id": "cd6d4616", "metadata": {}, "outputs": [], "source": ["GPT_CONFIG_124M = {\n", "    \"vocab_size\": 50257,\n", "    \"context_length\": 1024,\n", "    \"emb_dim\": 768,\n", "    \"n_heads\": 12,\n", "    \"n_layers\": 12,\n", "    \"drop_rate\": 0.1,\n", "    \"qkv_bias\": <PERSON><PERSON><PERSON>,\n", "}"]}, {"cell_type": "code", "execution_count": 2, "id": "49d690be-4659-4a0c-a687-972620488a04", "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "\n", "class DummyGPTModel(nn.Module):\n", "    def __init__(self, cfg):\n", "        super().__init__()\n", "        self.tok_emb = nn.Embedding(cfg[\"vocab_size\"], cfg[\"emb_dim\"])\n", "        self.pos_emb = nn.Embedding(cfg[\"context_length\"], cfg[\"emb_dim\"])\n", "        self.drop_emb = nn.Dropout(cfg[\"drop_rate\"])\n", "\n", "        self.trf_blocks = nn.Sequential(\n", "            *[DummyTransformerBlock(cfg) for _ in range(cfg[\"n_layers\"])]\n", "        )\n", "\n", "        self.final_norm = DummyLayerNorm(cfg[\"emb_dim\"])\n", "        self.out_head = nn.Linear(cfg[\"emb_dim\"], cfg[\"vocab_size\"], bias=False)\n", "\n", "    def forward(self, in_idx):\n", "        batch_size, seq_len = in_idx.shape\n", "        tok_embeds = self.tok_emb(in_idx)\n", "        pos_embeds = self.pos_emb(torch.arange(seq_len, device=in_idx.device))\n", "        x = tok_embeds + pos_embeds\n", "        x = self.drop_emb(x)\n", "        x = self.trf_blocks(x)\n", "        x = self.final_norm(x)\n", "        logits = self.out_head(x)\n", "        return logits\n", "\n", "class DummyTransformerBlock(nn.Module):\n", "    def __init__(self, cfg):\n", "        super().__init__()\n", "        self.dummy_linear = nn.Linear(cfg[\"emb_dim\"], cfg[\"emb_dim\"])\n", "\n", "    def forward(self, x):\n", "        return self.dummy_linear(x)\n", "\n", "class DummyLayerNorm(nn.Module):\n", "    def __init__(self, normalized_shape, eps=1e-5):\n", "        super().__init__()\n", "        self.norm = nn.LayerNorm(normalized_shape, eps=eps)\n", "\n", "    def forward(self, x):\n", "        return self.norm(x)\n"]}, {"cell_type": "code", "execution_count": 3, "id": "906cdf31", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[15745,  6942, 19523,   481],\n", "        [15745,  2163, 18305,   261]])\n"]}], "source": ["import tiktoken\n", "\n", "tokenizer = tiktoken.get_encoding(\"o200k_base\")\n", "\n", "batch = []\n", "\n", "txt1 = \"Every effort moves you\"\n", "txt2 = \"Every day holds a\"\n", "\n", "batch.append(torch.tensor(tokenizer.encode(txt1)))\n", "batch.append(torch.tensor(tokenizer.encode(txt2)))\n", "batch = torch.stack(batch, dim=0)\n", "print(batch)"]}, {"cell_type": "code", "execution_count": 4, "id": "11d7e851-990d-4d60-9c43-991b1faef572", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output shape: torch.Size([2, 4, 50257])\n", "tensor([[[-6.4775e-01, -1.0351e+00,  7.1715e-04,  ..., -3.0386e-01,\n", "          -1.4683e-01,  1.1515e-01],\n", "         [-6.2048e-01, -9.8547e-01, -1.0306e-01,  ..., -3.0466e-01,\n", "          -2.2802e-01,  4.5229e-02],\n", "         [-6.5014e-01, -9.2747e-01, -6.0722e-02,  ..., -3.3743e-01,\n", "          -1.5788e-01,  1.0084e-01],\n", "         [-6.6215e-01, -9.1074e-01,  2.8211e-02,  ..., -2.5992e-01,\n", "          -1.7543e-01,  3.7387e-02]],\n", "\n", "        [[-6.3283e-01, -1.0436e+00,  2.8532e-02,  ..., -2.8849e-01,\n", "          -1.2913e-01,  1.0073e-01],\n", "         [-6.2790e-01, -9.4095e-01, -7.9501e-02,  ..., -2.7654e-01,\n", "          -2.8042e-01, -2.6469e-02],\n", "         [-6.4704e-01, -9.0190e-01, -6.9532e-02,  ..., -3.2600e-01,\n", "          -2.7654e-01, -1.9126e-02],\n", "         [-6.8202e-01, -8.7647e-01,  8.0229e-02,  ..., -3.0066e-01,\n", "          -1.9041e-01,  1.1801e-01]]], grad_fn=<UnsafeViewBackward0>)\n"]}], "source": ["torch.manual_seed(123)\n", "model = DummyGPTModel(cfg=GPT_CONFIG_124M)\n", "\n", "logits = model(batch)\n", "print(\"Output shape:\", logits.shape)\n", "print(logits)"]}, {"cell_type": "markdown", "id": "c1483e62-ac81-4b6e-b466-f1b861c5ef8e", "metadata": {}, "source": ["### 4.2 Normalizing activations withlayers normalization"]}, {"cell_type": "code", "execution_count": 5, "id": "a9ef25e3-3a5b-4b71-8e7a-27b314f393b1", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[-0.1115,  0.1204, -0.3696, -0.2404, -1.1969],\n", "        [ 0.2093, -0.9724, -0.7550,  0.3239, -0.1085]])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.manual_seed(123)\n", "\n", "batch_example = torch.randn(2, 5)\n", "batch_example"]}, {"cell_type": "code", "execution_count": 6, "id": "45e5906b-29b3-47a1-8ad9-a50d1a7703e4", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.2260, 0.3470, 0.0000, 0.2216, 0.0000, 0.0000],\n", "        [0.2133, 0.2394, 0.0000, 0.5198, 0.3297, 0.0000]],\n", "       grad_fn=<ReluBackward0>)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["layer = nn.Sequential(nn.<PERSON>ar(5, 6), nn.ReLU())\n", "out = layer(batch_example)\n", "out"]}, {"cell_type": "code", "execution_count": 7, "id": "6602ddf4-5d6d-4c12-958e-11c2b2895ebf", "metadata": {}, "outputs": [{"data": {"text/plain": ["<PERSON>.<PERSON><PERSON>([2, 6])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["out.shape"]}, {"cell_type": "code", "execution_count": 8, "id": "123dd751-7351-4b8e-aae1-9a3387bf77c0", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.1324],\n", "        [0.2170]], grad_fn=<MeanBackward1>)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["mean = out.mean(dim=-1, keepdim=True)\n", "mean"]}, {"cell_type": "code", "execution_count": 9, "id": "417e6d44-e557-4570-b4bd-0fc47ba9e6d0", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.0231],\n", "        [0.0398]], grad_fn=<VarBackward0>)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["var = out.var(dim=-1, keepdim=True)\n", "var"]}, {"cell_type": "code", "execution_count": 10, "id": "c740be42-fdfa-479a-8e3a-39901a2e384b", "metadata": {}, "outputs": [], "source": ["class LayerNorm(nn.Module):\n", "    def __init__(self, emb_dim):\n", "        super().__init__()\n", "        self.eps = 1e-5\n", "        self.scale = nn.Parameter(torch.ones(emb_dim))\n", "        self.shift = nn.Parameter(torch.zeros(emb_dim))\n", "\n", "    def forward(self, x):\n", "        mean = x.mean(dim=-1, keepdim=True)\n", "        var = x.var(dim=-1, keepdim=True, unbiased=False)\n", "        norm_x = (x - mean) / torch.sqrt(var + self.eps)\n", "        return self.scale * norm_x + self.shift"]}, {"cell_type": "markdown", "id": "efe5e358-9b5a-43b6-a082-c5a63f6b7d25", "metadata": {}, "source": ["### 4.3 Implementing a feed forwards network with GELU activations"]}, {"cell_type": "code", "execution_count": 11, "id": "fa307668-0011-4e0c-9b98-083cfbbdf58a", "metadata": {}, "outputs": [], "source": ["class GELU(nn.Module):\n", "    def __init__(self):\n", "        super().__init__()\n", "\n", "    def forward(self, x):\n", "        return 0,5 * x * (1 + torch.tanh(\n", "            torch.sqrt(torch.tensor(2.0 / torch.pi)) *\n", "            (x + 0.044715 * torch.pow(x, 3))\n", "        ))\n", "\n"]}, {"cell_type": "code", "execution_count": 12, "id": "cfa79cac", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x300 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "gelu, relu = nn.GELU(), nn.ReLU()\n", "\n", "x = torch.linspace(-3, 3, 100)\n", "y_gelu, y_relu = gelu(x), relu(x)\n", "\n", "plt.figure(figsize=(8, 3))\n", "for i, (y, label) in enumerate(zip([y_gelu, y_relu], [\"GELU\", \"ReLU\"]), 1):\n", "    \n", "    plt.subplot(1, 2, i)\n", "    plt.plot(x, y)\n", "    plt.title(f\"{label} Activation Function\")\n", "    plt.xlabel(\"x\")\n", "    plt.ylabel(f\"{label}(x)\")\n", "    plt.grid(True)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 13, "id": "7d5a72ad-caa7-4fb2-a5fd-076c9c703a0a", "metadata": {}, "outputs": [], "source": ["class FeedForward(nn.Module):\n", "    def __init__(self, cfg):\n", "        super().__init__()\n", "        self.layers = nn.Sequential(\n", "            nn.Linear(cfg[\"emb_dim\"], 4 * cfg[\"emb_dim\"]),\n", "            nn.GELU(),\n", "            nn.Linear(4 * cfg[\"emb_dim\"], cfg[\"emb_dim\"]),\n", "        )\n", "\n", "    def forward(self, x):\n", "        return self.layers(x)\n"]}, {"cell_type": "code", "execution_count": 14, "id": "8a148135-3fce-430b-9c14-fa2ca34bc91e", "metadata": {}, "outputs": [], "source": ["ffn = FeedForward(GPT_CONFIG_124M)"]}, {"cell_type": "code", "execution_count": 15, "id": "b50957fb", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([2, 3, 768])"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["x = torch.rand(2, 3, 768)\n", "ffn(x).shape"]}, {"cell_type": "code", "execution_count": 16, "id": "8b8afdac-185d-4307-bd18-9a2c5f0237bd", "metadata": {}, "outputs": [{"data": {"text/plain": ["Parameter containing:\n", "tensor([[-0.0223,  0.0308,  0.0022,  ..., -0.0174, -0.0227, -0.0178],\n", "        [-0.0027, -0.0043, -0.0274,  ...,  0.0079,  0.0112,  0.0010],\n", "        [-0.0029,  0.0175,  0.0244,  ..., -0.0164, -0.0300,  0.0045],\n", "        ...,\n", "        [ 0.0255,  0.0023,  0.0277,  ..., -0.0037, -0.0134, -0.0284],\n", "        [ 0.0266,  0.0236, -0.0193,  ...,  0.0120,  0.0061, -0.0261],\n", "        [ 0.0222,  0.0106, -0.0190,  ..., -0.0253,  0.0320, -0.0109]],\n", "       requires_grad=True)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["ffn.layers[0].weight"]}, {"cell_type": "markdown", "id": "261461fb-4aa4-4d0b-86fc-fd09b1b2c4ba", "metadata": {}, "source": ["### 4.4 Adding Shortcut Connections"]}, {"cell_type": "code", "execution_count": 17, "id": "ef091e15", "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "\n", "class ExampleDeepNeuralNetwork(nn.Module):\n", "    def __init__(self, layer_sizes, use_shortcut):\n", "        super().__init__()\n", "        self.use_shortcut = use_shortcut\n", "        self.layers = nn.ModuleList()\n", "        for i in range(len(layer_sizes) - 1):\n", "            self.layers.append(\n", "                nn.Sequential(\n", "                    nn.Linear(layer_sizes[i], layer_sizes[i+1]),\n", "                    nn.GELU()\n", "                )\n", "            )\n", "\n", "    def forward(self, x):\n", "        for layer in self.layers:\n", "            layer_output = layer(x)\n", "            if self.use_shortcut and x.shape == layer_output.shape:\n", "                x = x + layer_output\n", "            else:\n", "                x = layer_output\n", "        return x\n", "\n", "def print_gradients(model, x):\n", "    output = model(x)\n", "    target = torch.zeros_like(output) \n", "\n", "    loss_fn = nn.MSELoss()\n", "    loss = loss_fn(output, target)\n", "    loss.backward()\n", "\n", "    for name, param in model.named_parameters():\n", "        if param.grad is not None and 'weight' in name:\n", "            print(f\"{name} has gradient mean of {param.grad.abs().mean().item()}\")\n"]}, {"cell_type": "code", "execution_count": 18, "id": "778f8941", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["layers.0.0.weight has gradient mean of 0.00020174124801997095\n", "layers.1.0.weight has gradient mean of 0.00012011774379061535\n", "layers.2.0.weight has gradient mean of 0.0007152438047342002\n", "layers.3.0.weight has gradient mean of 0.0013988513965159655\n", "layers.4.0.weight has gradient mean of 0.005049603525549173\n"]}], "source": ["layer_sizes = [3, 3, 3, 3, 3, 1]\n", "sample_input = torch.tensor([[1., 0., -1.]])\n", "\n", "torch.manual_seed(123)\n", "model_without_shortcut = ExampleDeepNeuralNetwork(\n", "    layer_sizes, use_shortcut=False\n", ")\n", "print_gradients(model_without_shortcut, sample_input)\n"]}, {"cell_type": "code", "execution_count": 19, "id": "43e2b54b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["layers.0.0.weight has gradient mean of 0.22186797857284546\n", "layers.1.0.weight has gradient mean of 0.20709271728992462\n", "layers.2.0.weight has gradient mean of 0.3292388319969177\n", "layers.3.0.weight has gradient mean of 0.2667771577835083\n", "layers.4.0.weight has gradient mean of 1.3268064260482788\n"]}], "source": ["layer_sizes = [3, 3, 3, 3, 3, 1]\n", "sample_input = torch.tensor([[1., 0., -1.]])\n", "\n", "torch.manual_seed(123)\n", "model_without_shortcut = ExampleDeepNeuralNetwork(\n", "    layer_sizes, use_shortcut=True\n", ")\n", "print_gradients(model_without_shortcut, sample_input)\n"]}, {"cell_type": "markdown", "id": "90f2efe6", "metadata": {}, "source": ["### 4.5 Connecting attention and linear layers in a transformer block"]}, {"cell_type": "code", "execution_count": 20, "id": "271c9db6", "metadata": {}, "outputs": [], "source": ["from previous_chapters import MultiHeadAttention \n", "\n", "class TransformerBlock(nn.Module):\n", "    def __init__(self, cfg):\n", "        super().__init__()\n", "\n", "        self.att = MultiHeadAttention(\n", "            d_in=cfg[\"emb_dim\"],\n", "            d_out=cfg[\"emb_dim\"],\n", "            context_length=cfg[\"context_length\"],\n", "            dropout=cfg[\"drop_rate\"],\n", "            num_heads=cfg[\"n_heads\"],\n", "            qkv_bias=cfg[\"qkv_bias\"],\n", "        )\n", "\n", "        self.ff = FeedForward(cfg)\n", "        self.norm1 = nn.LayerNorm(cfg[\"emb_dim\"])\n", "        self.norm2 = nn.LayerNorm(cfg[\"emb_dim\"])\n", "        self.drop_shortcut = nn.Dropout(cfg[\"drop_rate\"])\n", "\n", "    def forward(self, x):\n", "        shortcut = x\n", "        x = self.norm1(x)\n", "        x = self.att(x)\n", "        x = self.drop_shortcut(x)\n", "        x = x + shortcut\n", "\n", "        shortcut = x\n", "        x = self.norm2(x)\n", "        x = self.ff(x)\n", "        x = self.drop_shortcut(x)\n", "        x = x + shortcut\n", "\n", "        return x\n"]}, {"cell_type": "code", "execution_count": 21, "id": "41ae32cb", "metadata": {}, "outputs": [], "source": ["torch.manual_seed(123)\n", "\n", "x = torch.randn(2, 4, 768)\n", "block = TransformerBlock(GPT_CONFIG_124M)\n", "output = block(x)"]}, {"cell_type": "code", "execution_count": 22, "id": "0b3206dc", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([2, 4, 768])"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["x.shape\n", "output.shape"]}, {"cell_type": "markdown", "id": "65246010", "metadata": {}, "source": ["### 4.6 CODING THE GPT MODEL"]}, {"cell_type": "code", "execution_count": 23, "id": "68bb0e47", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[15745,  6942, 19523,   481],\n", "        [15745,  2163, 18305,   261]])\n"]}], "source": ["import tiktoken\n", "\n", "tokenizer = tiktoken.get_encoding(\"o200k_base\")\n", "\n", "batch = []\n", "\n", "txt1 = \"Every effort moves you\"\n", "txt2 = \"Every day holds a\"\n", "\n", "batch.append(torch.tensor(tokenizer.encode(txt1)))\n", "batch.append(torch.tensor(tokenizer.encode(txt2)))\n", "batch = torch.stack(batch, dim=0)\n", "print(batch)"]}, {"cell_type": "code", "execution_count": 24, "id": "de171504", "metadata": {}, "outputs": [], "source": ["class GPTModel(nn.Module):\n", "    def __init__(self, cfg):\n", "        super().__init__()\n", "        self.tok_emb = nn.Embedding(cfg[\"vocab_size\"], cfg[\"emb_dim\"])\n", "        self.pos_emb = nn.Embedding(cfg[\"context_length\"], cfg[\"emb_dim\"])\n", "        self.drop_emb = nn.Dropout(cfg[\"drop_rate\"])\n", "\n", "        self.trf_blocks = nn.Sequential(\n", "            *[TransformerBlock(cfg) for _ in range(cfg[\"n_layers\"])]\n", "        )\n", "\n", "        self.final_norm = LayerNorm(cfg[\"emb_dim\"])\n", "        self.out_head = nn.Linear(cfg[\"emb_dim\"], cfg[\"vocab_size\"], bias=False)\n", "\n", "    def forward(self, in_idx):\n", "        batch_size, seq_len = in_idx.shape\n", "        tok_embeds = self.tok_emb(in_idx)\n", "        pos_embeds = self.pos_emb(torch.arange(seq_len, device=in_idx.device))\n", "        x = tok_embeds + pos_embeds\n", "        x = self.drop_emb(x)\n", "        x = self.trf_blocks(x)\n", "        x = self.final_norm(x)\n", "        logits = self.out_head(x)\n", "        return logits\n"]}, {"cell_type": "code", "execution_count": 25, "id": "d45947a9", "metadata": {}, "outputs": [], "source": ["torch.manual_seed(123)\n", "\n", "model = GPTModel(GPT_CONFIG_124M)\n", "out = model(batch)\n"]}, {"cell_type": "code", "execution_count": 26, "id": "db95b8fd", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([50257, 768])"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["model.tok_emb.weight.shape"]}, {"cell_type": "code", "execution_count": 27, "id": "2e3a3290", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([50257, 768])"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["model.out_head.weight.shape"]}, {"cell_type": "code", "execution_count": 28, "id": "370db39f", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([2, 4])"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["batch.shape"]}, {"cell_type": "code", "execution_count": 29, "id": "42f96975", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.Size([2, 4, 50257])"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["out.shape"]}, {"cell_type": "code", "execution_count": 30, "id": "6607c8fe", "metadata": {}, "outputs": [{"data": {"text/plain": ["8"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["batch.numel()"]}, {"cell_type": "code", "execution_count": 31, "id": "bc0c7af3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["163,009,536\n"]}], "source": ["total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)\n", "print(f\"{total_params:,}\")\n"]}, {"cell_type": "markdown", "id": "36898a61", "metadata": {}, "source": ["### 4.7 Generating Text"]}, {"cell_type": "code", "execution_count": 32, "id": "bb9d2e34", "metadata": {}, "outputs": [], "source": ["def generate_text_simple(model, idx, max_new_tokens, context_size):\n", "    for _ in range(max_new_tokens):\n", "        idx_cond = idx[:, -context_size:]\n", "\n", "        with torch.no_grad():\n", "            logits = model(idx_cond)\n", "        logits = logits[:, -1, :]\n", "\n", "\n", "        probs = torch.softmax(logits, dim=-1)\n", "\n", "        idx_next = torch.argmax(probs, dim=-1, keepdim=True)\n", "        idx = torch.cat((idx, idx_next), dim=1)\n", "\n", "    return idx"]}, {"cell_type": "code", "execution_count": 33, "id": "1e16acb0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["encoded: [13225, 11, 357, 939]\n"]}], "source": ["start_context = \"Hello, I am\"\n", "\n", "encoded = tokenizer.encode(start_context)\n", "print(\"encoded:\", encoded)\n"]}, {"cell_type": "code", "execution_count": 34, "id": "a17dabe6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["encoded_tensor.shape: torch.Size([1, 4])\n"]}], "source": ["encoded_tensor = torch.tensor(encoded).unsqueeze(0)\n", "print(\"encoded_tensor.shape:\", encoded_tensor.shape)"]}, {"cell_type": "code", "execution_count": 35, "id": "d890689c", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[13225,    11,   357,   939]])"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["encoded_tensor"]}, {"cell_type": "code", "execution_count": 36, "id": "63e3c9e2", "metadata": {}, "outputs": [], "source": ["out = generate_text_simple(\n", "    model = model,\n", "    idx = encoded_tensor,\n", "    max_new_tokens = 10,\n", "    context_size = GPT_CONFIG_124M[\"context_length\"]\n", ")"]}, {"cell_type": "code", "execution_count": 37, "id": "3d105061", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[13225,    11,   357,   939,  7156, 29289,  8238,  4551, 47978, 24619,\n", "         46463,  8712, 35564, 22890]])"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["out"]}, {"cell_type": "code", "execution_count": 38, "id": "f16ff3b1", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Hello, I amతелаship.Forms traj talksρου shall добав նախ'"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer.decode(out.squeeze(0).tolist())"]}], "metadata": {"kernelspec": {"display_name": "AIbYME", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}