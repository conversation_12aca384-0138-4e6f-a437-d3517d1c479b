#!/usr/bin/env python3
"""
Featureimanbug AI - Setup script for easy installation.
"""
import os
import sys
import subprocess
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Command: {command}")
        print(f"   Error: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 3.8+ required, found {version.major}.{version.minor}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True


def install_dependencies():
    """Install required dependencies."""
    print("📦 Installing dependencies...")
    
    # Check if pip is available
    if not run_command("pip --version", "Checking pip"):
        print("❌ pip is not available. Please install pip first.")
        return False
    
    # Install requirements
    if not run_command("pip install -r requirements.txt", "Installing requirements"):
        return False
    
    return True


def create_directories():
    """Create necessary directories."""
    print("📁 Creating directories...")
    
    directories = [
        "checkpoints",
        "chat_history", 
        "examples/checkpoints",
        "examples/chat_history",
        "data"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"   Created: {directory}")
    
    print("✅ Directories created successfully")
    return True


def create_sample_data():
    """Create sample data file for testing."""
    print("📄 Creating sample data file...")
    
    sample_text = """
The quick brown fox jumps over the lazy dog. This is a sample text file for training 
the Featureimanbug AI language model. In the world of artificial intelligence, 
language models have become increasingly important for various natural language 
processing tasks.

Machine learning and deep learning have revolutionized the field of AI. Transformers, 
in particular, have shown remarkable success in language understanding and generation 
tasks. The attention mechanism allows models to focus on relevant parts of the input 
sequence when processing information.

Training a language model requires careful consideration of various factors such as 
model architecture, training data quality, optimization techniques, and evaluation 
metrics. The goal is to create a model that can understand and generate human-like 
text that is both coherent and contextually appropriate.

Natural language processing encompasses many tasks including text classification, 
sentiment analysis, machine translation, question answering, and text summarization. 
Each of these tasks benefits from the powerful representations learned by large 
language models during pre-training on diverse text corpora.

The future of AI looks promising with continued advances in model architectures, 
training techniques, and computational resources. As models become more capable, 
they will likely find applications in education, healthcare, creative writing, 
programming assistance, and many other domains.
""" * 10  # Repeat for more training data
    
    data_file = Path("data/sample_data.txt")
    with open(data_file, 'w', encoding='utf-8') as f:
        f.write(sample_text.strip())
    
    print(f"✅ Sample data created: {data_file}")
    return True


def test_installation():
    """Test the installation by running a simple import."""
    print("🧪 Testing installation...")
    
    try:
        # Test imports
        import torch
        import tiktoken
        import tqdm
        
        # Test our modules
        sys.path.insert(0, "src")
        from src.config import GPTConfig
        from src.model.gpt import GPTModel
        
        print("✅ All imports successful")
        
        # Test basic functionality
        config = GPTConfig()
        model = GPTModel(config)
        print(f"✅ Model created with {model.get_num_params():,} parameters")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False


def main():
    """Main setup function."""
    print("🚀 Featureimanbug AI Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies")
        sys.exit(1)
    
    # Create directories
    if not create_directories():
        print("❌ Failed to create directories")
        sys.exit(1)
    
    # Create sample data
    if not create_sample_data():
        print("❌ Failed to create sample data")
        sys.exit(1)
    
    # Test installation
    if not test_installation():
        print("❌ Installation test failed")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("=" * 50)
    print("\n📚 Quick Start:")
    print("1. Train a model:     python main.py train --data-path data/sample_data.txt")
    print("2. Start chat:        python main.py chat")
    print("3. Generate text:     python main.py generate --prompt 'Hello world'")
    print("4. Run examples:      python examples/simple_training.py")
    print("\n📖 For more information, see README.md")
    print("🤖 Welcome to Featureimanbug AI!")


if __name__ == "__main__":
    main()
