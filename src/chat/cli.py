"""
Featureimanbug AI - Command-line chat interface.
"""
import os
import sys
import argparse
from typing import Optional
import readline  # For better input handling

from .interface import ChatInterface


class ChatCLI:
    """Command-line interface for Featureimanbug AI chat."""

    def __init__(self, chat_interface: ChatInterface):
        self.chat = chat_interface
        self.running = True

        # CLI colors
        self.COLORS = {
            'user': '\033[94m',      # Blue
            'assistant': '\033[92m',  # Green
            'system': '\033[93m',     # Yellow
            'error': '\033[91m',      # Red
            'reset': '\033[0m',       # Reset
            'bold': '\033[1m',        # Bold
        }

    def print_colored(self, text: str, color: str = 'reset') -> None:
        """Print colored text."""
        print(f"{self.COLORS.get(color, '')}{text}{self.COLORS['reset']}")

    def print_welcome(self) -> None:
        """Print welcome message."""
        self.print_colored("=" * 60, 'bold')
        self.print_colored("🤖 Welcome to Featureimanbug AI Chat Interface", 'bold')
        self.print_colored("=" * 60, 'bold')
        print()
        self.print_colored("Commands:", 'system')
        self.print_colored("  /help     - Show this help message", 'system')
        self.print_colored("  /new      - Start a new conversation", 'system')
        self.print_colored("  /list     - List all conversations", 'system')
        self.print_colored("  /switch   - Switch to a different conversation", 'system')
        self.print_colored("  /delete   - Delete a conversation", 'system')
        self.print_colored("  /search   - Search conversations", 'system')
        self.print_colored("  /settings - Show/update generation settings", 'system')
        self.print_colored("  /export   - Export conversations", 'system')
        self.print_colored("  /stats    - Show chat statistics", 'system')
        self.print_colored("  /quit     - Exit the chat", 'system')
        print()
        self.print_colored("Type your message and press Enter to chat!", 'system')
        print()

    def handle_command(self, command: str) -> bool:
        """
        Handle special commands.

        Returns:
            True if command was handled, False otherwise
        """
        command = command.strip().lower()

        if command == '/help':
            self.print_welcome()
            return True

        elif command == '/new':
            title = input("Enter conversation title (or press Enter for default): ").strip()
            if not title:
                title = "New Conversation"
            conv_id = self.chat.start_new_conversation(title)
            self.print_colored(f"✅ Started new conversation: {title} (ID: {conv_id[:8]}...)", 'system')
            return True

        elif command == '/list':
            conversations = self.chat.get_conversation_list()
            if not conversations:
                self.print_colored("No conversations found.", 'system')
            else:
                self.print_colored("📋 Your Conversations:", 'system')
                for i, conv in enumerate(conversations[:10], 1):
                    current = "👉 " if conv['conversation_id'] == self.chat.conversation_manager.current_conversation_id else "   "
                    self.print_colored(f"{current}{i}. {conv['title']} ({conv['message_count']} messages)", 'system')
            return True

        elif command == '/switch':
            conversations = self.chat.get_conversation_list()
            if not conversations:
                self.print_colored("No conversations available.", 'error')
                return True

            self.print_colored("Select a conversation:", 'system')
            for i, conv in enumerate(conversations[:10], 1):
                self.print_colored(f"{i}. {conv['title']}", 'system')

            try:
                choice = int(input("Enter number: ")) - 1
                if 0 <= choice < len(conversations):
                    conv_id = conversations[choice]['conversation_id']
                    self.chat.conversation_manager.set_current_conversation(conv_id)
                    self.print_colored(f"✅ Switched to: {conversations[choice]['title']}", 'system')
                else:
                    self.print_colored("Invalid selection.", 'error')
            except ValueError:
                self.print_colored("Invalid input.", 'error')
            return True

        elif command == '/delete':
            conversations = self.chat.get_conversation_list()
            if not conversations:
                self.print_colored("No conversations to delete.", 'error')
                return True

            self.print_colored("Select a conversation to delete:", 'system')
            for i, conv in enumerate(conversations[:10], 1):
                self.print_colored(f"{i}. {conv['title']}", 'system')

            try:
                choice = int(input("Enter number: ")) - 1
                if 0 <= choice < len(conversations):
                    conv_id = conversations[choice]['conversation_id']
                    confirm = input(f"Delete '{conversations[choice]['title']}'? (y/N): ").strip().lower()
                    if confirm == 'y':
                        self.chat.delete_conversation(conv_id)
                        self.print_colored("✅ Conversation deleted.", 'system')
                    else:
                        self.print_colored("Deletion cancelled.", 'system')
                else:
                    self.print_colored("Invalid selection.", 'error')
            except ValueError:
                self.print_colored("Invalid input.", 'error')
            return True

        elif command == '/search':
            query = input("Enter search query: ").strip()
            if query:
                results = self.chat.search_conversations(query)
                if results:
                    self.print_colored(f"🔍 Search results for '{query}':", 'system')
                    for result in results:
                        self.print_colored(f"📄 {result['title']}", 'system')
                        for match in result['matches']:
                            self.print_colored(f"   {match}", 'system')
                        print()
                else:
                    self.print_colored("No results found.", 'system')
            return True

        elif command == '/settings':
            settings = self.chat.generation_settings
            self.print_colored("⚙️  Current Generation Settings:", 'system')
            for key, value in settings.items():
                self.print_colored(f"  {key}: {value}", 'system')

            update = input("Update settings? (y/N): ").strip().lower()
            if update == 'y':
                try:
                    temp = input(f"Temperature ({settings['temperature']}): ").strip()
                    if temp:
                        settings['temperature'] = float(temp)

                    top_k = input(f"Top-k ({settings['top_k']}): ").strip()
                    if top_k:
                        settings['top_k'] = int(top_k)

                    top_p = input(f"Top-p ({settings['top_p']}): ").strip()
                    if top_p:
                        settings['top_p'] = float(top_p)

                    max_tokens = input(f"Max tokens ({settings['max_new_tokens']}): ").strip()
                    if max_tokens:
                        settings['max_new_tokens'] = int(max_tokens)

                    self.chat.update_generation_settings(**settings)
                    self.print_colored("✅ Settings updated.", 'system')
                except ValueError:
                    self.print_colored("Invalid input. Settings not updated.", 'error')
            return True

        elif command == '/export':
            filename = input("Enter filename (default: chat_export.json): ").strip()
            if not filename:
                filename = "chat_export.json"

            format_type = input("Format (json/txt, default: json): ").strip().lower()
            if format_type not in ['json', 'txt']:
                format_type = 'json'

            try:
                self.chat.export_conversations(filename, format_type)
                self.print_colored(f"✅ Conversations exported to {filename}", 'system')
            except Exception as e:
                self.print_colored(f"❌ Export failed: {e}", 'error')
            return True

        elif command == '/stats':
            stats = self.chat.get_statistics()
            self.print_colored("📊 Chat Statistics:", 'system')
            self.print_colored(f"  Total conversations: {stats['total_conversations']}", 'system')
            self.print_colored(f"  Total messages: {stats['total_messages']}", 'system')
            if stats['total_conversations'] > 0:
                self.print_colored(f"  Average messages per conversation: {stats['average_messages_per_conversation']:.1f}", 'system')
                if stats['oldest_conversation']:
                    self.print_colored(f"  Oldest conversation: {stats['oldest_conversation']['title']}", 'system')
                if stats['newest_conversation']:
                    self.print_colored(f"  Most recent: {stats['newest_conversation']['title']}", 'system')
            return True

        elif command in ['/quit', '/exit', '/q']:
            self.print_colored("👋 Goodbye! Thanks for using Featureimanbug AI!", 'system')
            self.running = False
            return True

        return False

    def run(self) -> None:
        """Run the chat CLI."""
        self.print_welcome()

        # Start a conversation if none exists
        if not self.chat.conversation_manager.get_current_conversation():
            self.chat.start_new_conversation()
            self.print_colored("🆕 Started a new conversation.", 'system')
            print()

        while self.running:
            try:
                # Get user input
                user_input = input(f"{self.COLORS['user']}You: {self.COLORS['reset']}").strip()

                if not user_input:
                    continue

                # Handle commands
                if user_input.startswith('/'):
                    self.handle_command(user_input)
                    continue

                # Generate response
                print(f"{self.COLORS['assistant']}Featureimanbug AI: {self.COLORS['reset']}", end="", flush=True)

                try:
                    # Use streaming for better UX
                    response_parts = []
                    for chunk in self.chat.stream_response(user_input):
                        print(chunk, end="", flush=True)
                        response_parts.append(chunk)
                    print()  # New line after response

                except Exception as e:
                    # Fallback to non-streaming
                    self.print_colored(f"Streaming failed, using standard generation: {e}", 'error')
                    response = self.chat.send_message(user_input)
                    self.print_colored(response, 'assistant')

                print()  # Extra line for readability

            except KeyboardInterrupt:
                print()
                self.print_colored("👋 Goodbye! Thanks for using Featureimanbug AI!", 'system')
                break
            except EOFError:
                print()
                self.print_colored("👋 Goodbye! Thanks for using Featureimanbug AI!", 'system')
                break
            except Exception as e:
                self.print_colored(f"❌ Error: {e}", 'error')
                print()


def main():
    """Main entry point for CLI."""
    import argparse
    import sys
    import os

    # Add src to path for imports
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

    parser = argparse.ArgumentParser(description='Featureimanbug AI Chat Interface')
    parser.add_argument('--model-path', type=str, help='Path to trained model')
    parser.add_argument('--config-path', type=str, help='Path to model config')
    parser.add_argument('--history-dir', type=str, default='chat_history',
                       help='Directory for chat history')

    args = parser.parse_args()

    try:
        # Import and initialize components
        import torch
        import tiktoken
        from src.config import GPTConfig
        from src.model.gpt import GPTModel
        from src.generation.generator import TextGenerator
        from src.chat.interface import ChatInterface

        # Load model and config
        if args.model_path and args.config_path:
            config = GPTConfig.load(args.config_path)
            model = GPTModel(config)
            model.load_state_dict(torch.load(args.model_path, map_location='cpu'))
        else:
            print("⚠️  No model specified, using default small model (untrained)")
            from src.config import GPT_CONFIG_SMALL
            config = GPT_CONFIG_SMALL
            model = GPTModel(config)

        # Set device
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model.to(device)
        model.eval()

        # Initialize tokenizer and generator
        tokenizer = tiktoken.get_encoding("gpt2")
        generator = TextGenerator(model, tokenizer)

        # Initialize chat interface
        chat_interface = ChatInterface(model, generator, args.history_dir)

        # Start CLI
        cli = ChatCLI(chat_interface)
        cli.run()

    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running from the project root directory.")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error initializing chat: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()