"""
Featureimanbug AI - Main chat interface implementation.
"""
import torch
from typing import Optional, Dict, Any, List
from datetime import datetime

from ..model.gpt import GPTModel
from ..generation.generator import TextGenerator
from .conversation import ConversationManager, Conversation
from .history import ChatHistory


class ChatInterface:
    """
    Featureimanbug AI ChatGPT-like conversational interface.
    
    Provides a user-friendly chat experience with conversation management,
    history tracking, and various generation options.
    """
    
    def __init__(self, 
                 model: GPTModel, 
                 generator: TextGenerator,
                 history_dir: str = "chat_history",
                 auto_save: bool = True):
        self.model = model
        self.generator = generator
        self.conversation_manager = ConversationManager()
        self.chat_history = ChatHistory(history_dir)
        self.auto_save = auto_save
        
        # Default generation settings
        self.generation_settings = {
            'max_new_tokens': 150,
            'strategy': 'temperature',
            'temperature': 0.8,
            'top_k': 50,
            'top_p': 0.9,
            'repetition_penalty': 1.1,
            'stream': True
        }
        
        # System prompt
        self.system_prompt = (
            "You are Featureimanbug AI, a helpful and knowledgeable AI assistant. "
            "You provide thoughtful, accurate, and engaging responses while being "
            "respectful and professional. You can help with a wide variety of tasks "
            "including answering questions, writing, analysis, coding, and creative tasks."
        )
        
        # Load existing conversations
        self._load_recent_conversations()
    
    def _load_recent_conversations(self, limit: int = 10) -> None:
        """Load recent conversations from history."""
        recent_conversations = self.chat_history.list_conversations(limit)
        
        for conv_info in recent_conversations:
            conversation = self.chat_history.load_conversation(conv_info['conversation_id'])
            if conversation:
                self.conversation_manager.conversations[conversation.conversation_id] = conversation
    
    def start_new_conversation(self, title: str = "New Conversation") -> str:
        """Start a new conversation."""
        conversation = self.conversation_manager.create_conversation(title)
        
        # Add system message
        conversation.add_message('system', self.system_prompt)
        
        if self.auto_save:
            self.chat_history.save_conversation(conversation)
        
        return conversation.conversation_id
    
    def send_message(self, message: str, conversation_id: Optional[str] = None) -> str:
        """
        Send a message and get AI response.
        
        Args:
            message: User message
            conversation_id: Conversation ID (uses current if None)
            
        Returns:
            AI response
        """
        # Get or create conversation
        if conversation_id:
            conversation = self.conversation_manager.get_conversation(conversation_id)
            if not conversation:
                raise ValueError(f"Conversation {conversation_id} not found")
            self.conversation_manager.set_current_conversation(conversation_id)
        else:
            conversation = self.conversation_manager.get_current_conversation()
            if not conversation:
                conversation_id = self.start_new_conversation()
                conversation = self.conversation_manager.get_conversation(conversation_id)
        
        # Add user message
        conversation.add_message('user', message)
        
        # Generate response
        context = conversation.get_context(max_tokens=1500)
        response = self.generator.generate(
            prompt=context + f"\n\nHuman: {message}\n\nAssistant:",
            **self.generation_settings
        )
        
        # Extract just the assistant's response
        if "Assistant:" in response:
            response = response.split("Assistant:")[-1].strip()
        
        # Add assistant message
        conversation.add_message('assistant', response)
        
        # Auto-save if enabled
        if self.auto_save:
            self.chat_history.save_conversation(conversation)
        
        return response

    def stream_response(self, message: str, conversation_id: Optional[str] = None):
        """
        Send a message and stream the AI response.

        Args:
            message: User message
            conversation_id: Conversation ID (uses current if None)

        Yields:
            Chunks of AI response as they're generated
        """
        # Get or create conversation
        if conversation_id:
            conversation = self.conversation_manager.get_conversation(conversation_id)
            if not conversation:
                raise ValueError(f"Conversation {conversation_id} not found")
            self.conversation_manager.set_current_conversation(conversation_id)
        else:
            conversation = self.conversation_manager.get_current_conversation()
            if not conversation:
                conversation_id = self.start_new_conversation()
                conversation = self.conversation_manager.get_conversation(conversation_id)

        # Add user message
        conversation.add_message('user', message)

        # Generate streaming response
        context = conversation.get_context(max_tokens=1500)
        prompt = context + f"\n\nHuman: {message}\n\nAssistant:"

        # Collect response chunks
        response_chunks = []
        settings = self.generation_settings.copy()
        settings['stream'] = True

        for chunk in self.generator._stream_generate(
            torch.tensor(self.generator.tokenizer.encode(prompt), device=self.generator.device).unsqueeze(0),
            settings['max_new_tokens'],
            self.generator.strategies[settings['strategy']]() if settings['strategy'] == 'greedy'
            else self.generator.strategies[settings['strategy']](settings.get('temperature', 0.8)),
            settings.get('repetition_penalty', 1.0),
            None
        ):
            response_chunks.append(chunk)
            yield chunk

        # Add complete response to conversation
        full_response = ''.join(response_chunks)
        conversation.add_message('assistant', full_response)

        # Auto-save if enabled
        if self.auto_save:
            self.chat_history.save_conversation(conversation)

    def update_generation_settings(self, **kwargs) -> None:
        """Update generation settings."""
        self.generation_settings.update(kwargs)

    def get_conversation_list(self) -> List[Dict[str, Any]]:
        """Get list of all conversations."""
        return self.conversation_manager.list_conversations()

    def delete_conversation(self, conversation_id: str) -> bool:
        """Delete a conversation."""
        success = self.conversation_manager.delete_conversation(conversation_id)
        if success:
            self.chat_history.delete_conversation(conversation_id)
        return success

    def search_conversations(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search conversations by content."""
        return self.chat_history.search_conversations(query, limit)

    def export_conversations(self, output_file: str, format: str = 'json') -> None:
        """Export all conversations."""
        self.chat_history.export_conversations(output_file, format)

    def get_statistics(self) -> Dict[str, Any]:
        """Get chat statistics."""
        return self.chat_history.get_statistics()
