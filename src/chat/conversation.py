"""
Featureimanbug AI - Conversation management for chat interface.
"""
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime
import json
import uuid


@dataclass
class Message:
    """Represents a single message in a conversation."""
    role: str  # 'user', 'assistant', 'system'
    content: str
    timestamp: datetime = field(default_factory=datetime.now)
    message_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary."""
        return {
            'role': self.role,
            'content': self.content,
            'timestamp': self.timestamp.isoformat(),
            'message_id': self.message_id,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """Create message from dictionary."""
        return cls(
            role=data['role'],
            content=data['content'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            message_id=data['message_id'],
            metadata=data.get('metadata', {})
        )


@dataclass
class Conversation:
    """Represents a conversation with multiple messages."""
    conversation_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    title: str = "New Conversation"
    messages: List[Message] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_message(self, role: str, content: str, metadata: Optional[Dict[str, Any]] = None) -> Message:
        """Add a new message to the conversation."""
        message = Message(
            role=role,
            content=content,
            metadata=metadata or {}
        )
        self.messages.append(message)
        self.updated_at = datetime.now()
        
        # Auto-generate title from first user message
        if not self.title or self.title == "New Conversation":
            if role == 'user' and len(content.strip()) > 0:
                self.title = content.strip()[:50] + ("..." if len(content.strip()) > 50 else "")
        
        return message
    
    def get_context(self, max_tokens: int = 2048, include_system: bool = True) -> str:
        """
        Get conversation context as a formatted string.
        
        Args:
            max_tokens: Maximum number of tokens to include
            include_system: Whether to include system messages
            
        Returns:
            Formatted conversation context
        """
        context_parts = []
        
        # Add system messages if requested
        if include_system:
            system_messages = [msg for msg in self.messages if msg.role == 'system']
            for msg in system_messages:
                context_parts.append(f"System: {msg.content}")
        
        # Add conversation messages in reverse order (most recent first)
        conversation_messages = [msg for msg in self.messages if msg.role != 'system']
        
        for msg in reversed(conversation_messages):
            if msg.role == 'user':
                context_parts.insert(-len([p for p in context_parts if p.startswith('System:')]) if include_system else 0, 
                                   f"Human: {msg.content}")
            elif msg.role == 'assistant':
                context_parts.insert(-len([p for p in context_parts if p.startswith('System:')]) if include_system else 0,
                                   f"Assistant: {msg.content}")
        
        # Join and truncate if necessary
        context = "\n\n".join(context_parts)
        
        # Simple token estimation (rough approximation)
        estimated_tokens = len(context.split())
        if estimated_tokens > max_tokens:
            # Truncate from the beginning (keep recent messages)
            words = context.split()
            context = " ".join(words[-max_tokens:])
        
        return context
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert conversation to dictionary."""
        return {
            'conversation_id': self.conversation_id,
            'title': self.title,
            'messages': [msg.to_dict() for msg in self.messages],
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Conversation':
        """Create conversation from dictionary."""
        return cls(
            conversation_id=data['conversation_id'],
            title=data['title'],
            messages=[Message.from_dict(msg_data) for msg_data in data['messages']],
            created_at=datetime.fromisoformat(data['created_at']),
            updated_at=datetime.fromisoformat(data['updated_at']),
            metadata=data.get('metadata', {})
        )


class ConversationManager:
    """Manages multiple conversations and their state."""
    
    def __init__(self):
        self.conversations: Dict[str, Conversation] = {}
        self.current_conversation_id: Optional[str] = None
    
    def create_conversation(self, title: str = "New Conversation") -> Conversation:
        """Create a new conversation."""
        conversation = Conversation(title=title)
        self.conversations[conversation.conversation_id] = conversation
        self.current_conversation_id = conversation.conversation_id
        return conversation
    
    def get_conversation(self, conversation_id: str) -> Optional[Conversation]:
        """Get a conversation by ID."""
        return self.conversations.get(conversation_id)
    
    def get_current_conversation(self) -> Optional[Conversation]:
        """Get the current active conversation."""
        if self.current_conversation_id:
            return self.conversations.get(self.current_conversation_id)
        return None
    
    def set_current_conversation(self, conversation_id: str) -> bool:
        """Set the current active conversation."""
        if conversation_id in self.conversations:
            self.current_conversation_id = conversation_id
            return True
        return False
    
    def delete_conversation(self, conversation_id: str) -> bool:
        """Delete a conversation."""
        if conversation_id in self.conversations:
            del self.conversations[conversation_id]
            if self.current_conversation_id == conversation_id:
                self.current_conversation_id = None
            return True
        return False
    
    def list_conversations(self) -> List[Dict[str, Any]]:
        """List all conversations with basic info."""
        conversations = []
        for conv in self.conversations.values():
            conversations.append({
                'conversation_id': conv.conversation_id,
                'title': conv.title,
                'created_at': conv.created_at.isoformat(),
                'updated_at': conv.updated_at.isoformat(),
                'message_count': len(conv.messages)
            })
        
        # Sort by updated_at (most recent first)
        conversations.sort(key=lambda x: x['updated_at'], reverse=True)
        return conversations
    
    def save_to_file(self, filepath: str) -> None:
        """Save all conversations to a JSON file."""
        data = {
            'conversations': {
                conv_id: conv.to_dict() 
                for conv_id, conv in self.conversations.items()
            },
            'current_conversation_id': self.current_conversation_id
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def load_from_file(self, filepath: str) -> None:
        """Load conversations from a JSON file."""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.conversations = {
                conv_id: Conversation.from_dict(conv_data)
                for conv_id, conv_data in data.get('conversations', {}).items()
            }
            self.current_conversation_id = data.get('current_conversation_id')
            
        except (FileNotFoundError, json.JSONDecodeError):
            # If file doesn't exist or is corrupted, start fresh
            self.conversations = {}
            self.current_conversation_id = None
