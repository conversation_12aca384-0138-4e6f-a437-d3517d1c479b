"""
Featureimanbug AI - Chat history management.
"""
import os
import json
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from .conversation import Conversation, ConversationManager


class ChatHistory:
    """Manages persistent chat history storage and retrieval."""
    
    def __init__(self, history_dir: str = "chat_history"):
        self.history_dir = history_dir
        self.ensure_history_dir()
    
    def ensure_history_dir(self) -> None:
        """Ensure the history directory exists."""
        if not os.path.exists(self.history_dir):
            os.makedirs(self.history_dir)
    
    def save_conversation(self, conversation: Conversation) -> None:
        """Save a single conversation to disk."""
        filename = f"{conversation.conversation_id}.json"
        filepath = os.path.join(self.history_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(conversation.to_dict(), f, indent=2, ensure_ascii=False)
    
    def load_conversation(self, conversation_id: str) -> Optional[Conversation]:
        """Load a single conversation from disk."""
        filename = f"{conversation_id}.json"
        filepath = os.path.join(self.history_dir, filename)
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return Conversation.from_dict(data)
        except (FileNotFoundError, json.JSONDecodeError):
            return None
    
    def list_conversations(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """List all saved conversations."""
        conversations = []
        
        for filename in os.listdir(self.history_dir):
            if filename.endswith('.json'):
                conversation_id = filename[:-5]  # Remove .json extension
                conversation = self.load_conversation(conversation_id)
                
                if conversation:
                    conversations.append({
                        'conversation_id': conversation.conversation_id,
                        'title': conversation.title,
                        'created_at': conversation.created_at.isoformat(),
                        'updated_at': conversation.updated_at.isoformat(),
                        'message_count': len(conversation.messages)
                    })
        
        # Sort by updated_at (most recent first)
        conversations.sort(key=lambda x: x['updated_at'], reverse=True)
        
        if limit:
            conversations = conversations[:limit]
        
        return conversations
    
    def delete_conversation(self, conversation_id: str) -> bool:
        """Delete a conversation from disk."""
        filename = f"{conversation_id}.json"
        filepath = os.path.join(self.history_dir, filename)
        
        try:
            os.remove(filepath)
            return True
        except FileNotFoundError:
            return False
    
    def search_conversations(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search conversations by content."""
        results = []
        query_lower = query.lower()
        
        for filename in os.listdir(self.history_dir):
            if filename.endswith('.json'):
                conversation_id = filename[:-5]
                conversation = self.load_conversation(conversation_id)
                
                if conversation:
                    # Search in title and message content
                    matches = []
                    
                    if query_lower in conversation.title.lower():
                        matches.append(f"Title: {conversation.title}")
                    
                    for message in conversation.messages:
                        if query_lower in message.content.lower():
                            # Add context around the match
                            content = message.content
                            start_idx = content.lower().find(query_lower)
                            context_start = max(0, start_idx - 50)
                            context_end = min(len(content), start_idx + len(query) + 50)
                            context = content[context_start:context_end]
                            
                            matches.append(f"{message.role}: ...{context}...")
                    
                    if matches:
                        results.append({
                            'conversation_id': conversation.conversation_id,
                            'title': conversation.title,
                            'updated_at': conversation.updated_at.isoformat(),
                            'matches': matches[:3]  # Limit matches per conversation
                        })
        
        # Sort by relevance (number of matches) and recency
        results.sort(key=lambda x: (len(x['matches']), x['updated_at']), reverse=True)
        
        return results[:limit]
    
    def cleanup_old_conversations(self, days: int = 30) -> int:
        """Delete conversations older than specified days."""
        cutoff_date = datetime.now() - timedelta(days=days)
        deleted_count = 0
        
        for filename in os.listdir(self.history_dir):
            if filename.endswith('.json'):
                conversation_id = filename[:-5]
                conversation = self.load_conversation(conversation_id)
                
                if conversation and conversation.updated_at < cutoff_date:
                    if self.delete_conversation(conversation_id):
                        deleted_count += 1
        
        return deleted_count
    
    def export_conversations(self, output_file: str, format: str = 'json') -> None:
        """Export all conversations to a single file."""
        conversations = []
        
        for filename in os.listdir(self.history_dir):
            if filename.endswith('.json'):
                conversation_id = filename[:-5]
                conversation = self.load_conversation(conversation_id)
                if conversation:
                    conversations.append(conversation.to_dict())
        
        if format.lower() == 'json':
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'export_date': datetime.now().isoformat(),
                    'conversation_count': len(conversations),
                    'conversations': conversations
                }, f, indent=2, ensure_ascii=False)
        
        elif format.lower() == 'txt':
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"Featureimanbug AI Chat Export\n")
                f.write(f"Export Date: {datetime.now().isoformat()}\n")
                f.write(f"Total Conversations: {len(conversations)}\n")
                f.write("=" * 50 + "\n\n")
                
                for conv_data in conversations:
                    conv = Conversation.from_dict(conv_data)
                    f.write(f"Conversation: {conv.title}\n")
                    f.write(f"Created: {conv.created_at.isoformat()}\n")
                    f.write(f"Updated: {conv.updated_at.isoformat()}\n")
                    f.write("-" * 30 + "\n")
                    
                    for message in conv.messages:
                        f.write(f"{message.role.title()}: {message.content}\n\n")
                    
                    f.write("=" * 50 + "\n\n")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get statistics about chat history."""
        conversations = self.list_conversations()
        
        if not conversations:
            return {
                'total_conversations': 0,
                'total_messages': 0,
                'oldest_conversation': None,
                'newest_conversation': None,
                'average_messages_per_conversation': 0
            }
        
        total_messages = sum(conv['message_count'] for conv in conversations)
        oldest = min(conversations, key=lambda x: x['created_at'])
        newest = max(conversations, key=lambda x: x['updated_at'])
        
        return {
            'total_conversations': len(conversations),
            'total_messages': total_messages,
            'oldest_conversation': {
                'title': oldest['title'],
                'created_at': oldest['created_at']
            },
            'newest_conversation': {
                'title': newest['title'],
                'updated_at': newest['updated_at']
            },
            'average_messages_per_conversation': total_messages / len(conversations)
        }
