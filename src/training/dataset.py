"""
Featureimanbug AI - Dataset utilities for training.
"""
import torch
from torch.utils.data import Dataset, DataLoader
import tiktoken
from typing import List, Optional, Union
import os


class GPTDataset(Dataset):
    """Dataset for GPT training with sliding window approach."""
    
    def __init__(self, 
                 text: str, 
                 tokenizer: tiktoken.Encoding, 
                 max_length: int, 
                 stride: int):
        """
        Initialize dataset.
        
        Args:
            text: Input text data
            tokenizer: Tokenizer for encoding text
            max_length: Maximum sequence length
            stride: Stride for sliding window
        """
        self.input_ids = []
        self.target_ids = []
        
        # Encode the entire text
        token_ids = tokenizer.encode(text, allowed_special={"<|endoftext|>"})
        
        # Create sliding windows
        for i in range(0, len(token_ids) - max_length, stride):
            input_chunk = token_ids[i:i + max_length]
            target_chunk = token_ids[i + 1:i + max_length + 1]
            
            self.input_ids.append(torch.tensor(input_chunk, dtype=torch.long))
            self.target_ids.append(torch.tensor(target_chunk, dtype=torch.long))

    def __len__(self) -> int:
        return len(self.input_ids)

    def __getitem__(self, idx: int) -> tuple:
        return self.input_ids[idx], self.target_ids[idx]


class MultiFileGPTDataset(Dataset):
    """Dataset that can handle multiple text files."""
    
    def __init__(self,
                 file_paths: List[str],
                 tokenizer: tiktoken.Encoding,
                 max_length: int,
                 stride: int,
                 max_files: Optional[int] = None):
        """
        Initialize multi-file dataset.
        
        Args:
            file_paths: List of text file paths
            tokenizer: Tokenizer for encoding text
            max_length: Maximum sequence length
            stride: Stride for sliding window
            max_files: Maximum number of files to process
        """
        self.input_ids = []
        self.target_ids = []
        
        # Process files
        files_processed = 0
        for file_path in file_paths:
            if max_files and files_processed >= max_files:
                break
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    text = f.read()
                
                # Create dataset for this file
                file_dataset = GPTDataset(text, tokenizer, max_length, stride)
                
                # Add to main dataset
                self.input_ids.extend(file_dataset.input_ids)
                self.target_ids.extend(file_dataset.target_ids)
                
                files_processed += 1
                print(f"Processed file {files_processed}: {file_path} ({len(file_dataset)} sequences)")
                
            except Exception as e:
                print(f"Error processing {file_path}: {e}")
                continue

    def __len__(self) -> int:
        return len(self.input_ids)

    def __getitem__(self, idx: int) -> tuple:
        return self.input_ids[idx], self.target_ids[idx]


def create_dataloader(dataset: Dataset,
                     batch_size: int = 8,
                     shuffle: bool = True,
                     drop_last: bool = True,
                     num_workers: int = 0,
                     pin_memory: bool = True) -> DataLoader:
    """
    Create a DataLoader for training.
    
    Args:
        dataset: Dataset instance
        batch_size: Batch size
        shuffle: Whether to shuffle data
        drop_last: Whether to drop last incomplete batch
        num_workers: Number of worker processes
        pin_memory: Whether to pin memory for faster GPU transfer
        
    Returns:
        DataLoader instance
    """
    return DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        drop_last=drop_last,
        num_workers=num_workers,
        pin_memory=pin_memory
    )


def load_text_data(file_path: str) -> str:
    """Load text data from file."""
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()


def prepare_dataset_from_directory(data_dir: str,
                                  tokenizer: tiktoken.Encoding,
                                  max_length: int = 1024,
                                  stride: int = 512,
                                  file_pattern: str = "*.txt",
                                  max_files: Optional[int] = None) -> Dataset:
    """
    Prepare dataset from a directory of text files.
    
    Args:
        data_dir: Directory containing text files
        tokenizer: Tokenizer instance
        max_length: Maximum sequence length
        stride: Stride for sliding window
        file_pattern: File pattern to match
        max_files: Maximum number of files to process
        
    Returns:
        Dataset instance
    """
    import glob
    
    # Find all matching files
    pattern = os.path.join(data_dir, file_pattern)
    file_paths = glob.glob(pattern)
    
    if not file_paths:
        raise ValueError(f"No files found matching pattern: {pattern}")
    
    print(f"Found {len(file_paths)} files matching pattern")
    
    # Create dataset
    return MultiFileGPTDataset(
        file_paths=file_paths,
        tokenizer=tokenizer,
        max_length=max_length,
        stride=stride,
        max_files=max_files
    )


def create_simple_dataloader(text: str,
                           tokenizer: tiktoken.Encoding,
                           batch_size: int = 8,
                           max_length: int = 256,
                           stride: int = 128,
                           shuffle: bool = True) -> DataLoader:
    """
    Create a simple dataloader from text string.
    
    Args:
        text: Input text
        tokenizer: Tokenizer instance
        batch_size: Batch size
        max_length: Maximum sequence length
        stride: Stride for sliding window
        shuffle: Whether to shuffle data
        
    Returns:
        DataLoader instance
    """
    dataset = GPTDataset(text, tokenizer, max_length, stride)
    return create_dataloader(dataset, batch_size=batch_size, shuffle=shuffle)
