"""
Featureimanbug AI - Main training class.
"""
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import os
import time
from typing import Optional, Dict, Any, Callable
from tqdm import tqdm

from ..model.gpt import GPTModel
from ..config import GPTConfig
from .optimizer import create_optimizer, create_scheduler, GradientClipping, EMA
from .utils import (
    save_checkpoint, TrainingMetrics, log_training_step, 
    estimate_remaining_time, format_time, print_model_info
)


class GPTTrainer:
    """
    Featureimanbug AI GPT trainer with advanced features.
    
    Includes gradient clipping, learning rate scheduling, checkpointing,
    and comprehensive logging.
    """
    
    def __init__(self,
                 model: GPTModel,
                 config: GPTConfig,
                 device: Optional[torch.device] = None,
                 checkpoint_dir: str = "checkpoints",
                 log_interval: int = 10,
                 save_interval: int = 1000,
                 eval_interval: int = 500,
                 max_grad_norm: float = 1.0,
                 use_ema: bool = False,
                 ema_decay: float = 0.999):
        """
        Initialize trainer.
        
        Args:
            model: GPT model to train
            config: Training configuration
            device: Device to train on
            checkpoint_dir: Directory for saving checkpoints
            log_interval: Steps between logging
            save_interval: Steps between saving checkpoints
            eval_interval: Steps between evaluation
            max_grad_norm: Maximum gradient norm for clipping
            use_ema: Whether to use exponential moving average
            ema_decay: EMA decay rate
        """
        self.model = model
        self.config = config
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.checkpoint_dir = checkpoint_dir
        self.log_interval = log_interval
        self.save_interval = save_interval
        self.eval_interval = eval_interval
        
        # Move model to device
        self.model.to(self.device)
        
        # Initialize training components
        self.optimizer = create_optimizer(
            model=self.model,
            learning_rate=config.learning_rate,
            weight_decay=config.weight_decay
        )
        
        self.scheduler = create_scheduler(
            optimizer=self.optimizer,
            warmup_steps=config.warmup_steps,
            max_steps=config.max_epochs * 1000  # Rough estimate
        )
        
        self.grad_clipper = GradientClipping(max_norm=max_grad_norm)
        
        # EMA if requested
        self.ema = EMA(model, decay=ema_decay) if use_ema else None
        
        # Training state
        self.current_epoch = 0
        self.current_step = 0
        self.best_loss = float('inf')
        self.training_start_time = None
        
        # Metrics tracking
        self.metrics = TrainingMetrics()
        self.training_log = []
        
        # Create checkpoint directory
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        # Print model info
        print_model_info(self.model)

    def train_epoch(self,
                   dataloader: DataLoader,
                   epoch: int,
                   validation_dataloader: Optional[DataLoader] = None) -> Dict[str, float]:
        """
        Train for one epoch.

        Args:
            dataloader: Training dataloader
            epoch: Current epoch number
            validation_dataloader: Validation dataloader (optional)

        Returns:
            Dictionary of epoch metrics
        """
        self.model.train()
        epoch_start_time = time.time()
        epoch_losses = []

        # Progress bar
        pbar = tqdm(dataloader, desc=f"Epoch {epoch}")

        for batch_idx, (input_ids, target_ids) in enumerate(pbar):
            step_start_time = time.time()

            # Move data to device
            input_ids = input_ids.to(self.device)
            target_ids = target_ids.to(self.device)

            # Forward pass
            logits, loss = self.model(input_ids, target_ids)

            # Backward pass
            self.optimizer.zero_grad()
            loss.backward()

            # Gradient clipping
            grad_norm = self.grad_clipper.clip_gradients(self.model)

            # Optimizer step
            self.optimizer.step()

            # Scheduler step
            if self.scheduler:
                self.scheduler.step()

            # EMA update
            if self.ema:
                self.ema.update(self.model)

            # Update metrics
            step_time = time.time() - step_start_time
            current_lr = self.optimizer.param_groups[0]['lr']

            self.metrics.update(
                loss=loss.item(),
                step_time=step_time,
                learning_rate=current_lr,
                gradient_norm=grad_norm
            )

            epoch_losses.append(loss.item())

            # Logging
            if self.current_step % self.log_interval == 0:
                log_training_step(
                    step=self.current_step,
                    epoch=epoch,
                    loss=loss.item(),
                    learning_rate=current_lr,
                    step_time=step_time,
                    gradient_norm=grad_norm,
                    log_interval=1  # Always log when called
                )

                # Update progress bar
                avg_metrics = self.metrics.get_averages()
                pbar.set_postfix({
                    'loss': f"{avg_metrics.get('avg_loss', 0):.4f}",
                    'ppl': f"{avg_metrics.get('avg_perplexity', 0):.2f}",
                    'lr': f"{current_lr:.2e}"
                })

            # Save checkpoint
            if self.current_step % self.save_interval == 0 and self.current_step > 0:
                self.save_checkpoint(epoch, loss.item())

            # Validation
            if (validation_dataloader and
                self.current_step % self.eval_interval == 0 and
                self.current_step > 0):
                val_loss = self.validate(validation_dataloader)
                self.model.train()  # Back to training mode

                # Save best model
                if val_loss < self.best_loss:
                    self.best_loss = val_loss
                    self.save_checkpoint(epoch, val_loss, is_best=True)

            self.current_step += 1

        # Epoch summary
        epoch_time = time.time() - epoch_start_time
        avg_loss = sum(epoch_losses) / len(epoch_losses)

        epoch_metrics = {
            'epoch': epoch,
            'avg_loss': avg_loss,
            'epoch_time': epoch_time,
            'steps': len(dataloader)
        }

        print(f"\nEpoch {epoch} completed in {format_time(epoch_time)}")
        print(f"Average loss: {avg_loss:.4f}")

        return epoch_metrics
