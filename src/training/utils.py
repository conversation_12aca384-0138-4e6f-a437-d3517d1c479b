"""
Featureimanbug AI - Training utilities and helper functions.
"""
import torch
import torch.nn as nn
import os
import json
import time
from typing import Dict, Any, Optional, Tuple
from datetime import datetime
import math


def save_checkpoint(model: nn.Module,
                   optimizer: torch.optim.Optimizer,
                   scheduler: Optional[torch.optim.lr_scheduler._LRScheduler],
                   epoch: int,
                   step: int,
                   loss: float,
                   config: Dict[str, Any],
                   checkpoint_dir: str,
                   filename: Optional[str] = None) -> str:
    """
    Save training checkpoint.
    
    Args:
        model: Model to save
        optimizer: Optimizer state
        scheduler: Learning rate scheduler state
        epoch: Current epoch
        step: Current step
        loss: Current loss
        config: Model configuration
        checkpoint_dir: Directory to save checkpoint
        filename: Custom filename (optional)
        
    Returns:
        Path to saved checkpoint
    """
    os.makedirs(checkpoint_dir, exist_ok=True)
    
    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"checkpoint_epoch_{epoch}_step_{step}_{timestamp}.pt"
    
    checkpoint_path = os.path.join(checkpoint_dir, filename)
    
    checkpoint = {
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict() if scheduler else None,
        'epoch': epoch,
        'step': step,
        'loss': loss,
        'config': config,
        'timestamp': datetime.now().isoformat(),
        'pytorch_version': torch.__version__
    }
    
    torch.save(checkpoint, checkpoint_path)
    print(f"Checkpoint saved: {checkpoint_path}")
    
    return checkpoint_path


def load_checkpoint(checkpoint_path: str,
                   model: nn.Module,
                   optimizer: Optional[torch.optim.Optimizer] = None,
                   scheduler: Optional[torch.optim.lr_scheduler._LRScheduler] = None,
                   device: Optional[torch.device] = None) -> Dict[str, Any]:
    """
    Load training checkpoint.
    
    Args:
        checkpoint_path: Path to checkpoint file
        model: Model to load state into
        optimizer: Optimizer to load state into (optional)
        scheduler: Scheduler to load state into (optional)
        device: Device to load checkpoint on
        
    Returns:
        Checkpoint metadata
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    checkpoint = torch.load(checkpoint_path, map_location=device)
    
    # Load model state
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # Load optimizer state if provided
    if optimizer and 'optimizer_state_dict' in checkpoint:
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    
    # Load scheduler state if provided
    if scheduler and 'scheduler_state_dict' in checkpoint and checkpoint['scheduler_state_dict']:
        scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
    
    metadata = {
        'epoch': checkpoint.get('epoch', 0),
        'step': checkpoint.get('step', 0),
        'loss': checkpoint.get('loss', float('inf')),
        'config': checkpoint.get('config', {}),
        'timestamp': checkpoint.get('timestamp', ''),
        'pytorch_version': checkpoint.get('pytorch_version', '')
    }
    
    print(f"Checkpoint loaded: {checkpoint_path}")
    print(f"Epoch: {metadata['epoch']}, Step: {metadata['step']}, Loss: {metadata['loss']:.4f}")
    
    return metadata


def calculate_perplexity(loss: float) -> float:
    """Calculate perplexity from cross-entropy loss."""
    return math.exp(loss)


def format_time(seconds: float) -> str:
    """Format time in seconds to human-readable string."""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}h"


def estimate_remaining_time(elapsed_time: float, 
                          current_step: int, 
                          total_steps: int) -> str:
    """Estimate remaining training time."""
    if current_step == 0:
        return "Unknown"
    
    time_per_step = elapsed_time / current_step
    remaining_steps = total_steps - current_step
    remaining_time = time_per_step * remaining_steps
    
    return format_time(remaining_time)


class TrainingMetrics:
    """Track and compute training metrics."""
    
    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.reset()
    
    def reset(self):
        """Reset all metrics."""
        self.losses = []
        self.step_times = []
        self.learning_rates = []
        self.gradient_norms = []
    
    def update(self, 
               loss: float, 
               step_time: float, 
               learning_rate: float, 
               gradient_norm: Optional[float] = None):
        """Update metrics with new values."""
        self.losses.append(loss)
        self.step_times.append(step_time)
        self.learning_rates.append(learning_rate)
        
        if gradient_norm is not None:
            self.gradient_norms.append(gradient_norm)
        
        # Keep only recent values
        if len(self.losses) > self.window_size:
            self.losses = self.losses[-self.window_size:]
            self.step_times = self.step_times[-self.window_size:]
            self.learning_rates = self.learning_rates[-self.window_size:]
            
        if len(self.gradient_norms) > self.window_size:
            self.gradient_norms = self.gradient_norms[-self.window_size:]
    
    def get_averages(self) -> Dict[str, float]:
        """Get average metrics over the window."""
        if not self.losses:
            return {}
        
        metrics = {
            'avg_loss': sum(self.losses) / len(self.losses),
            'avg_perplexity': calculate_perplexity(sum(self.losses) / len(self.losses)),
            'avg_step_time': sum(self.step_times) / len(self.step_times),
            'current_lr': self.learning_rates[-1] if self.learning_rates else 0.0
        }
        
        if self.gradient_norms:
            metrics['avg_grad_norm'] = sum(self.gradient_norms) / len(self.gradient_norms)
        
        return metrics


def log_training_step(step: int,
                     epoch: int,
                     loss: float,
                     learning_rate: float,
                     step_time: float,
                     gradient_norm: Optional[float] = None,
                     log_interval: int = 10) -> None:
    """Log training step information."""
    if step % log_interval == 0:
        perplexity = calculate_perplexity(loss)
        
        log_msg = (
            f"Epoch {epoch:3d} | Step {step:6d} | "
            f"Loss: {loss:.4f} | PPL: {perplexity:.2f} | "
            f"LR: {learning_rate:.2e} | Time: {step_time:.3f}s"
        )
        
        if gradient_norm is not None:
            log_msg += f" | Grad Norm: {gradient_norm:.3f}"
        
        print(log_msg)


def save_training_log(log_data: list, log_file: str) -> None:
    """Save training log to JSON file."""
    with open(log_file, 'w') as f:
        json.dump(log_data, f, indent=2)


def load_training_log(log_file: str) -> list:
    """Load training log from JSON file."""
    try:
        with open(log_file, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        return []


def get_model_size_mb(model: nn.Module) -> float:
    """Get model size in megabytes."""
    param_size = 0
    buffer_size = 0
    
    for param in model.parameters():
        param_size += param.nelement() * param.element_size()
    
    for buffer in model.buffers():
        buffer_size += buffer.nelement() * buffer.element_size()
    
    size_mb = (param_size + buffer_size) / 1024 / 1024
    return size_mb


def count_parameters(model: nn.Module) -> Tuple[int, int]:
    """
    Count total and trainable parameters.
    
    Returns:
        Tuple of (total_params, trainable_params)
    """
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    return total_params, trainable_params


def print_model_info(model: nn.Module) -> None:
    """Print comprehensive model information."""
    total_params, trainable_params = count_parameters(model)
    model_size = get_model_size_mb(model)
    
    print("=" * 50)
    print("MODEL INFORMATION")
    print("=" * 50)
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")
    print(f"Model size: {model_size:.2f} MB")
    print("=" * 50)
