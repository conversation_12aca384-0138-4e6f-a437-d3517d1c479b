"""
Featureimanbug AI - Optimizer and scheduler utilities.
"""
import torch
import torch.nn as nn
from torch.optim import <PERSON><PERSON>, <PERSON>
from torch.optim.lr_scheduler import (
    CosineAnnealingLR, 
    LinearLR, 
    SequentialLR,
    LambdaLR
)
import math
from typing import Optional, Union


def create_optimizer(model: nn.Module,
                    learning_rate: float = 3e-4,
                    weight_decay: float = 0.01,
                    betas: tuple = (0.9, 0.95),
                    eps: float = 1e-8,
                    optimizer_type: str = 'adamw') -> torch.optim.Optimizer:
    """
    Create optimizer with proper weight decay handling.
    
    Args:
        model: Model to optimize
        learning_rate: Learning rate
        weight_decay: Weight decay coefficient
        betas: Adam beta parameters
        eps: Adam epsilon parameter
        optimizer_type: Type of optimizer ('adamw' or 'adam')
        
    Returns:
        Optimizer instance
    """
    # Separate parameters that should and shouldn't have weight decay
    decay_params = []
    no_decay_params = []
    
    for name, param in model.named_parameters():
        if param.requires_grad:
            # Don't apply weight decay to biases, layer norms, and embeddings
            if any(nd in name for nd in ['bias', 'norm', 'emb']):
                no_decay_params.append(param)
            else:
                decay_params.append(param)
    
    # Create parameter groups
    param_groups = [
        {'params': decay_params, 'weight_decay': weight_decay},
        {'params': no_decay_params, 'weight_decay': 0.0}
    ]
    
    if optimizer_type.lower() == 'adamw':
        return AdamW(param_groups, lr=learning_rate, betas=betas, eps=eps)
    elif optimizer_type.lower() == 'adam':
        return Adam(param_groups, lr=learning_rate, betas=betas, eps=eps)
    else:
        raise ValueError(f"Unknown optimizer type: {optimizer_type}")


def create_scheduler(optimizer: torch.optim.Optimizer,
                    scheduler_type: str = 'cosine_with_warmup',
                    warmup_steps: int = 1000,
                    max_steps: int = 10000,
                    min_lr_ratio: float = 0.1) -> Optional[torch.optim.lr_scheduler._LRScheduler]:
    """
    Create learning rate scheduler.
    
    Args:
        optimizer: Optimizer instance
        scheduler_type: Type of scheduler
        warmup_steps: Number of warmup steps
        max_steps: Maximum number of training steps
        min_lr_ratio: Minimum learning rate as ratio of initial LR
        
    Returns:
        Scheduler instance or None
    """
    if scheduler_type == 'none':
        return None
    
    elif scheduler_type == 'cosine_with_warmup':
        # Warmup phase
        warmup_scheduler = LinearLR(
            optimizer,
            start_factor=0.01,
            end_factor=1.0,
            total_iters=warmup_steps
        )
        
        # Cosine annealing phase
        cosine_steps = max_steps - warmup_steps
        cosine_scheduler = CosineAnnealingLR(
            optimizer,
            T_max=cosine_steps,
            eta_min=optimizer.param_groups[0]['lr'] * min_lr_ratio
        )
        
        # Combine schedulers
        return SequentialLR(
            optimizer,
            schedulers=[warmup_scheduler, cosine_scheduler],
            milestones=[warmup_steps]
        )
    
    elif scheduler_type == 'linear_warmup':
        def lr_lambda(step):
            if step < warmup_steps:
                return step / warmup_steps
            else:
                return max(min_lr_ratio, 1.0 - (step - warmup_steps) / (max_steps - warmup_steps))
        
        return LambdaLR(optimizer, lr_lambda)
    
    elif scheduler_type == 'cosine':
        return CosineAnnealingLR(
            optimizer,
            T_max=max_steps,
            eta_min=optimizer.param_groups[0]['lr'] * min_lr_ratio
        )
    
    else:
        raise ValueError(f"Unknown scheduler type: {scheduler_type}")


class GradientClipping:
    """Utility class for gradient clipping."""
    
    def __init__(self, max_norm: float = 1.0, norm_type: float = 2.0):
        self.max_norm = max_norm
        self.norm_type = norm_type
    
    def clip_gradients(self, model: nn.Module) -> float:
        """
        Clip gradients and return the gradient norm.
        
        Args:
            model: Model whose gradients to clip
            
        Returns:
            Gradient norm before clipping
        """
        return torch.nn.utils.clip_grad_norm_(
            model.parameters(),
            max_norm=self.max_norm,
            norm_type=self.norm_type
        ).item()


class EMA:
    """Exponential Moving Average for model parameters."""
    
    def __init__(self, model: nn.Module, decay: float = 0.999):
        self.decay = decay
        self.shadow = {}
        self.backup = {}
        
        # Initialize shadow parameters
        for name, param in model.named_parameters():
            if param.requires_grad:
                self.shadow[name] = param.data.clone()
    
    def update(self, model: nn.Module):
        """Update EMA parameters."""
        for name, param in model.named_parameters():
            if param.requires_grad and name in self.shadow:
                self.shadow[name] = (
                    self.decay * self.shadow[name] + 
                    (1.0 - self.decay) * param.data
                )
    
    def apply_shadow(self, model: nn.Module):
        """Apply EMA parameters to model."""
        for name, param in model.named_parameters():
            if param.requires_grad and name in self.shadow:
                self.backup[name] = param.data.clone()
                param.data = self.shadow[name]
    
    def restore(self, model: nn.Module):
        """Restore original parameters."""
        for name, param in model.named_parameters():
            if param.requires_grad and name in self.backup:
                param.data = self.backup[name]
        self.backup = {}


def get_parameter_count(model: nn.Module, trainable_only: bool = True) -> int:
    """Get the number of parameters in the model."""
    if trainable_only:
        return sum(p.numel() for p in model.parameters() if p.requires_grad)
    else:
        return sum(p.numel() for p in model.parameters())


def freeze_parameters(model: nn.Module, layer_names: list):
    """Freeze specific layers in the model."""
    for name, param in model.named_parameters():
        if any(layer_name in name for layer_name in layer_names):
            param.requires_grad = False


def unfreeze_parameters(model: nn.Module, layer_names: list):
    """Unfreeze specific layers in the model."""
    for name, param in model.named_parameters():
        if any(layer_name in name for layer_name in layer_names):
            param.requires_grad = True


def get_learning_rate(optimizer: torch.optim.Optimizer) -> float:
    """Get current learning rate from optimizer."""
    return optimizer.param_groups[0]['lr']


def set_learning_rate(optimizer: torch.optim.Optimizer, lr: float):
    """Set learning rate for all parameter groups."""
    for param_group in optimizer.param_groups:
        param_group['lr'] = lr
