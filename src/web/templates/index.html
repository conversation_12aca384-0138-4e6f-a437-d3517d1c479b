<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Featureimanbug AI - Chat Interface</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/9.1.2/marked.min.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <h2>🤖 Featureimanbug AI</h2>
                </div>
                <button class="new-chat-btn" id="newChatBtn">
                    <span class="icon">➕</span>
                    New Chat
                </button>
            </div>
            
            <div class="conversations-list" id="conversationsList">
                <!-- Conversations will be loaded here -->
            </div>
            
            <div class="sidebar-footer">
                <button class="export-btn" id="exportBtn">
                    <span class="icon">📥</span>
                    Export
                </button>
                <div class="model-status" id="modelStatus">
                    <span class="icon">🤖</span>
                    <span class="status-text">GPT-2 355M</span>
                </div>
            </div>
        </div>
        
        <!-- Main Chat Area -->
        <div class="main-content">
            <!-- Header -->
            <div class="chat-header">
                <button class="sidebar-toggle" id="sidebarToggle">☰</button>
                <div class="chat-title" id="chatTitle">Featureimanbug AI</div>
                <div class="status-indicator" id="statusIndicator">
                    <span class="status-dot"></span>
                    <span class="status-text">Connecting...</span>
                </div>
            </div>
            
            <!-- Messages Area -->
            <div class="messages-container" id="messagesContainer">
                <div class="welcome-message">
                    <div class="welcome-content">
                        <h1>🤖 Welcome to Featureimanbug AI</h1>
                        <p>Your personal GPT-2 powered assistant is ready to help!</p>
                        <div class="features">
                            <div class="feature">
                                <span class="feature-icon">💬</span>
                                <span>Natural conversations</span>
                            </div>
                            <div class="feature">
                                <span class="feature-icon">🧠</span>
                                <span>GPT-2 355M model</span>
                            </div>
                            <div class="feature">
                                <span class="feature-icon">💾</span>
                                <span>Conversation history</span>
                            </div>
                            <div class="feature">
                                <span class="feature-icon">⚙️</span>
                                <span>Customizable settings</span>
                            </div>
                        </div>
                        <p class="start-prompt">Start a conversation by typing a message below!</p>
                    </div>
                </div>
            </div>
            
            <!-- Input Area -->
            <div class="input-container">
                <div class="input-wrapper">
                    <textarea 
                        id="messageInput" 
                        placeholder="Type your message here... (Press Enter to send, Shift+Enter for new line)"
                        rows="1"
                        disabled
                    ></textarea>
                    <button id="sendBtn" class="send-btn" disabled>
                        <span class="send-icon">➤</span>
                    </button>
                </div>
                <div class="input-footer">
                    <span class="model-info" id="modelInfo">Loading model...</span>
                    <span class="typing-indicator" id="typingIndicator" style="display: none;">
                        <span class="dot"></span>
                        <span class="dot"></span>
                        <span class="dot"></span>
                        AI is typing...
                    </span>
                </div>
            </div>
        </div>
    </div>
    

    
    <!-- Export Modal -->
    <div class="modal" id="exportModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>📥 Export Conversations</h3>
                <button class="close-btn" id="closeExportBtn">&times;</button>
            </div>
            <div class="modal-body">
                <div class="setting-group">
                    <label for="exportFormat">Export Format:</label>
                    <select id="exportFormat">
                        <option value="json">JSON (structured data)</option>
                        <option value="txt">Text (readable format)</option>
                    </select>
                </div>
                <p>This will export all your conversations to a downloadable file.</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelExportBtn">Cancel</button>
                <button class="btn btn-primary" id="confirmExportBtn">Export</button>
            </div>
        </div>
    </div>
    
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <h3>Loading Featureimanbug AI...</h3>
            <p id="loadingMessage">Initializing model and chat interface...</p>
        </div>
    </div>
    
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
