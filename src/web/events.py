"""
Featureimanbug AI - SocketIO events for real-time communication.
"""
from flask_socketio import emit, disconnect
from flask import request
import json
from datetime import datetime

# Global references
chat_interface = None


def register_events(socketio):
    """Register SocketIO events."""
    
    @socketio.on('connect')
    def handle_connect():
        """Handle client connection."""
        print(f"🔌 Client connected: {request.sid}")
        emit('status', {
            'type': 'connected',
            'message': 'Connected to Featureimanbug AI',
            'timestamp': datetime.now().isoformat()
        })
    
    @socketio.on('disconnect')
    def handle_disconnect():
        """Handle client disconnection."""
        print(f"🔌 Client disconnected: {request.sid}")
    
    @socketio.on('send_message')
    def handle_message(data):
        """Handle incoming chat message with streaming response."""
        if chat_interface is None:
            emit('error', {
                'message': 'Chat interface not initialized',
                'timestamp': datetime.now().isoformat()
            })
            return
        
        try:
            message = data.get('message', '').strip()
            conversation_id = data.get('conversation_id')
            
            if not message:
                emit('error', {
                    'message': 'Message cannot be empty',
                    'timestamp': datetime.now().isoformat()
                })
                return
            
            # Emit message received confirmation
            emit('message_received', {
                'message': message,
                'timestamp': datetime.now().isoformat()
            })
            
            # Set current conversation if provided
            if conversation_id:
                chat_interface.conversation_manager.set_current_conversation(conversation_id)
            
            # Start streaming response
            emit('response_start', {
                'timestamp': datetime.now().isoformat()
            })
            
            # Generate streaming response
            response_chunks = []
            try:
                for chunk in chat_interface.stream_response(message, conversation_id):
                    response_chunks.append(chunk)
                    emit('response_chunk', {
                        'chunk': chunk,
                        'timestamp': datetime.now().isoformat()
                    })
                
                # Complete response
                full_response = ''.join(response_chunks)
                emit('response_complete', {
                    'full_response': full_response,
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as stream_error:
                # Fallback to non-streaming
                print(f"⚠️  Streaming failed, using fallback: {stream_error}")
                response = chat_interface.send_message(message, conversation_id)
                
                emit('response_chunk', {
                    'chunk': response,
                    'timestamp': datetime.now().isoformat()
                })
                
                emit('response_complete', {
                    'full_response': response,
                    'timestamp': datetime.now().isoformat()
                })
            
        except Exception as e:
            emit('error', {
                'message': str(e),
                'timestamp': datetime.now().isoformat()
            })
    
    @socketio.on('get_conversations')
    def handle_get_conversations():
        """Get list of conversations."""
        if chat_interface is None:
            emit('error', {'message': 'Chat interface not initialized'})
            return
        
        try:
            conversations = chat_interface.get_conversation_list()
            emit('conversations_list', {
                'conversations': conversations,
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            emit('error', {'message': str(e)})
    
    @socketio.on('create_conversation')
    def handle_create_conversation(data):
        """Create a new conversation."""
        if chat_interface is None:
            emit('error', {'message': 'Chat interface not initialized'})
            return
        
        try:
            title = data.get('title', 'New Conversation')
            conv_id = chat_interface.start_new_conversation(title)
            
            emit('conversation_created', {
                'conversation_id': conv_id,
                'title': title,
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            emit('error', {'message': str(e)})
    
    @socketio.on('switch_conversation')
    def handle_switch_conversation(data):
        """Switch to a different conversation."""
        if chat_interface is None:
            emit('error', {'message': 'Chat interface not initialized'})
            return
        
        try:
            conversation_id = data.get('conversation_id')
            success = chat_interface.conversation_manager.set_current_conversation(conversation_id)
            
            if success:
                conversation = chat_interface.conversation_manager.get_conversation(conversation_id)
                emit('conversation_switched', {
                    'conversation_id': conversation_id,
                    'conversation': conversation.to_dict() if conversation else None,
                    'timestamp': datetime.now().isoformat()
                })
            else:
                emit('error', {'message': 'Conversation not found'})
                
        except Exception as e:
            emit('error', {'message': str(e)})
    
    @socketio.on('delete_conversation')
    def handle_delete_conversation(data):
        """Delete a conversation."""
        if chat_interface is None:
            emit('error', {'message': 'Chat interface not initialized'})
            return
        
        try:
            conversation_id = data.get('conversation_id')
            success = chat_interface.delete_conversation(conversation_id)
            
            if success:
                emit('conversation_deleted', {
                    'conversation_id': conversation_id,
                    'timestamp': datetime.now().isoformat()
                })
            else:
                emit('error', {'message': 'Conversation not found'})
                
        except Exception as e:
            emit('error', {'message': str(e)})
    
    @socketio.on('update_settings')
    def handle_update_settings(data):
        """Update generation settings."""
        if chat_interface is None:
            emit('error', {'message': 'Chat interface not initialized'})
            return
        
        try:
            # Validate and update settings
            valid_settings = {
                'max_new_tokens', 'strategy', 'temperature', 
                'top_k', 'top_p', 'repetition_penalty'
            }
            
            settings_update = {}
            for key, value in data.items():
                if key in valid_settings:
                    settings_update[key] = value
            
            if settings_update:
                chat_interface.update_generation_settings(**settings_update)
                emit('settings_updated', {
                    'updated_settings': settings_update,
                    'current_settings': chat_interface.generation_settings,
                    'timestamp': datetime.now().isoformat()
                })
            else:
                emit('error', {'message': 'No valid settings provided'})
                
        except Exception as e:
            emit('error', {'message': str(e)})
    
    @socketio.on('ping')
    def handle_ping():
        """Handle ping for connection testing."""
        emit('pong', {
            'timestamp': datetime.now().isoformat()
        })


def set_chat_interface(interface):
    """Set the global chat interface for events."""
    global chat_interface
    chat_interface = interface
