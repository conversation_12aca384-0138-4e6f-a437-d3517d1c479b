"""
Featureimanbug AI - Flask web application.
"""
import os
from flask import Flask
from flask_socketio import SocketIO
from flask_cors import CORS


# Global SocketIO instance
socketio = SocketIO(cors_allowed_origins="*", async_mode='threading')


def create_app(config=None):
    """Create and configure Flask application."""
    app = Flask(__name__, 
                template_folder='templates',
                static_folder='static')
    
    # Configuration
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'featureimanbug-ai-secret-key-2024')
    app.config['DEBUG'] = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    
    # Apply custom config if provided
    if config:
        app.config.update(config)
    
    # Enable CORS
    CORS(app)
    
    # Initialize SocketIO with app
    socketio.init_app(app)
    
    # Register routes
    from .routes import register_routes
    register_routes(app)
    
    # Register SocketIO events
    from .events import register_events
    register_events(socketio)
    
    return app
