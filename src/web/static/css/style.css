/* Featureimanbug AI - Web Interface Styles */

:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --border-color: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --sidebar-width: 280px;
    --header-height: 60px;
    --input-height: 120px;
    --border-radius: 8px;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
}

.app-container {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

/* Sidebar Styles */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--surface-color);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.logo h2 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.new-chat-btn {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: background-color 0.2s;
}

.new-chat-btn:hover {
    background-color: var(--primary-hover);
}

.conversations-list {
    flex: 1;
    overflow-y: auto;
    padding: 0.5rem;
}

.conversation-item {
    padding: 0.75rem;
    margin-bottom: 0.25rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color 0.2s;
    border: 1px solid transparent;
}

.conversation-item:hover {
    background-color: var(--background-color);
}

.conversation-item.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.conversation-title {
    font-weight: 500;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.conversation-meta {
    font-size: 0.75rem;
    opacity: 0.7;
}

.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.export-btn {
    width: 100%;
    padding: 0.5rem 1rem;
    background-color: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s;
    margin-bottom: 0.5rem;
}

.export-btn:hover {
    background-color: var(--background-color);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.model-status {
    width: 100%;
    padding: 0.5rem 1rem;
    background-color: var(--background-color);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-align: center;
}

.model-status .icon {
    color: var(--success-color);
}

/* Main Content Styles */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-header {
    height: var(--header-height);
    background-color: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    padding: 0 1rem;
    gap: 1rem;
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: var(--text-secondary);
}

.chat-title {
    flex: 1;
    font-weight: 600;
    font-size: 1.125rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--warning-color);
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background-color: var(--success-color);
    animation: none;
}

.status-dot.error {
    background-color: var(--error-color);
    animation: none;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Messages Styles */
.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    scroll-behavior: smooth;
}

.welcome-message {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
}

.welcome-content h1 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 2rem;
}

.welcome-content p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    font-size: 1.125rem;
}

.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background-color: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.feature-icon {
    font-size: 1.5rem;
}

.start-prompt {
    color: var(--text-muted);
    font-style: italic;
}

.message {
    margin-bottom: 1.5rem;
    display: flex;
    gap: 0.75rem;
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background-color: var(--primary-color);
    color: white;
}

.message.assistant .message-avatar {
    background-color: var(--secondary-color);
    color: white;
}

.message-content {
    flex: 1;
    max-width: 70%;
}

.message.user .message-content {
    text-align: right;
}

.message-bubble {
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    background-color: var(--surface-color);
    box-shadow: var(--shadow);
    word-wrap: break-word;
}

.message.user .message-bubble {
    background-color: var(--primary-color);
    color: white;
    margin-left: auto;
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
}

/* Input Styles */
.input-container {
    background-color: var(--surface-color);
    border-top: 1px solid var(--border-color);
    padding: 1rem;
}

.input-wrapper {
    display: flex;
    gap: 0.75rem;
    align-items: flex-end;
}

#messageInput {
    flex: 1;
    min-height: 44px;
    max-height: 120px;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    resize: none;
    font-family: inherit;
    font-size: 0.875rem;
    line-height: 1.5;
    transition: border-color 0.2s;
}

#messageInput:focus {
    outline: none;
    border-color: var(--primary-color);
}

#messageInput:disabled {
    background-color: var(--background-color);
    color: var(--text-muted);
    cursor: not-allowed;
}

.send-btn {
    width: 44px;
    height: 44px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
    flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
    background-color: var(--primary-hover);
}

.send-btn:disabled {
    background-color: var(--text-muted);
    cursor: not-allowed;
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-muted);
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.typing-indicator .dot {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: var(--primary-color);
    animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator .dot:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator .dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-muted);
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s;
}

.close-btn:hover {
    background-color: var(--background-color);
    color: var(--text-primary);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
}

.setting-group {
    margin-bottom: 1.5rem;
}

.setting-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.setting-group input[type="range"] {
    width: 100%;
    margin-bottom: 0.25rem;
}

.setting-group select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--surface-color);
    color: var(--text-primary);
}

.setting-group small {
    color: var(--text-muted);
    font-size: 0.75rem;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
}

.btn-secondary {
    background-color: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--background-color);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-overlay.hidden {
    display: none;
}

.loading-content {
    text-align: center;
    max-width: 400px;
    padding: 2rem;
}

.spinner {
    width: 48px;
    height: 48px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.loading-content p {
    color: var(--text-secondary);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100%;
        z-index: 100;
        transform: translateX(-100%);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        width: 100%;
    }

    .sidebar-toggle {
        display: block;
    }

    .message-content {
        max-width: 85%;
    }

    .features {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }
}

/* Scrollbar Styles */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: var(--background-color);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}
