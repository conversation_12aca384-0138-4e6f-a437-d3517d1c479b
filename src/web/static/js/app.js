/**
 * Featureimanbug AI - Web Interface JavaScript
 */

class FeatureimanbugAI {
    constructor() {
        this.socket = null;
        this.currentConversationId = null;
        this.conversations = [];
        this.isConnected = false;
        this.isTyping = false;
        // Optimal preset settings for natural conversation
        this.settings = {
            temperature: 0.8,
            top_k: 50,
            top_p: 0.9,
            max_new_tokens: 100,
            strategy: 'temperature',
            repetition_penalty: 1.1
        };
        
        this.init();
    }
    
    init() {
        this.initializeSocket();
        this.bindEvents();
        this.showLoadingOverlay();
    }
    
    initializeSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            console.log('Connected to Featureimanbug AI');
            this.isConnected = true;
            this.updateStatus('connected', 'Connected');
            this.hideLoadingOverlay();
            this.enableInput();
            this.loadConversations();
        });
        
        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
            this.isConnected = false;
            this.updateStatus('error', 'Disconnected');
            this.disableInput();
        });
        
        this.socket.on('error', (error) => {
            console.error('Socket error:', error);
            this.showError('Connection error: ' + error.message);
        });
        
        // Message handling
        this.socket.on('message_received', (data) => {
            console.log('Message received confirmation');
        });
        
        this.socket.on('response_start', (data) => {
            this.showTypingIndicator();
            this.isTyping = true;
        });
        
        this.socket.on('response_chunk', (data) => {
            this.appendToLastMessage(data.chunk);
        });
        
        this.socket.on('response_complete', (data) => {
            this.hideTypingIndicator();
            this.isTyping = false;
            this.enableInput();
            this.scrollToBottom();
        });
        
        // Conversation management
        this.socket.on('conversations_list', (data) => {
            this.updateConversationsList(data.conversations);
        });
        
        this.socket.on('conversation_created', (data) => {
            this.currentConversationId = data.conversation_id;
            this.loadConversations();
            this.clearMessages();
            this.updateChatTitle(data.title);
        });
        
        this.socket.on('conversation_switched', (data) => {
            this.currentConversationId = data.conversation_id;
            this.loadConversationMessages(data.conversation);
            this.updateChatTitle(data.conversation.title);
        });
        
        this.socket.on('conversation_deleted', (data) => {
            this.loadConversations();
            if (this.currentConversationId === data.conversation_id) {
                this.currentConversationId = null;
                this.clearMessages();
                this.updateChatTitle('Featureimanbug AI');
            }
        });
        
        // Settings
        this.socket.on('settings_updated', (data) => {
            this.settings = data.current_settings;
            this.showSuccess('Settings updated successfully');
        });
        
        // Error handling
        this.socket.on('error', (data) => {
            this.showError(data.message);
            this.hideTypingIndicator();
            this.isTyping = false;
            this.enableInput();
        });
    }
    
    bindEvents() {
        // Message input
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        
        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        messageInput.addEventListener('input', () => {
            this.autoResizeTextarea(messageInput);
        });
        
        sendBtn.addEventListener('click', () => {
            this.sendMessage();
        });
        
        // Sidebar
        document.getElementById('newChatBtn').addEventListener('click', () => {
            this.createNewConversation();
        });
        
        document.getElementById('sidebarToggle').addEventListener('click', () => {
            this.toggleSidebar();
        });
        
        // Settings are now preset - no modal needed
        
        // Export modal
        document.getElementById('exportBtn').addEventListener('click', () => {
            this.openExportModal();
        });
        
        document.getElementById('closeExportBtn').addEventListener('click', () => {
            this.closeExportModal();
        });
        
        document.getElementById('cancelExportBtn').addEventListener('click', () => {
            this.closeExportModal();
        });
        
        document.getElementById('confirmExportBtn').addEventListener('click', () => {
            this.exportConversations();
        });
        
        // Close modals on outside click
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.classList.remove('show');
            }
        });
    }
    
    sendMessage() {
        const messageInput = document.getElementById('messageInput');
        const message = messageInput.value.trim();
        
        if (!message || !this.isConnected || this.isTyping) {
            return;
        }
        
        // Add user message to UI
        this.addMessage('user', message);
        
        // Clear input
        messageInput.value = '';
        this.autoResizeTextarea(messageInput);
        
        // Disable input while processing
        this.disableInput();
        
        // Send to server
        this.socket.emit('send_message', {
            message: message,
            conversation_id: this.currentConversationId
        });
        
        // Add placeholder for AI response
        this.addMessage('assistant', '', true);
        this.scrollToBottom();
    }
    
    addMessage(role, content, isPlaceholder = false) {
        const messagesContainer = document.getElementById('messagesContainer');
        const welcomeMessage = messagesContainer.querySelector('.welcome-message');
        
        // Remove welcome message if present
        if (welcomeMessage) {
            welcomeMessage.remove();
        }
        
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.textContent = role === 'user' ? '👤' : '🤖';
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        
        const bubble = document.createElement('div');
        bubble.className = 'message-bubble';
        
        if (isPlaceholder) {
            bubble.innerHTML = '<span class="typing-dots">...</span>';
            messageDiv.setAttribute('data-placeholder', 'true');
        } else {
            bubble.innerHTML = this.formatMessage(content);
        }
        
        const time = document.createElement('div');
        time.className = 'message-time';
        time.textContent = new Date().toLocaleTimeString();
        
        contentDiv.appendChild(bubble);
        contentDiv.appendChild(time);
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(contentDiv);
        
        messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();
    }
    
    appendToLastMessage(chunk) {
        const messagesContainer = document.getElementById('messagesContainer');
        const lastMessage = messagesContainer.querySelector('.message[data-placeholder="true"]');
        
        if (lastMessage) {
            const bubble = lastMessage.querySelector('.message-bubble');
            
            if (bubble.innerHTML === '<span class="typing-dots">...</span>') {
                bubble.innerHTML = '';
            }
            
            bubble.innerHTML += this.escapeHtml(chunk);
            this.scrollToBottom();
        }
    }
    
    formatMessage(content) {
        // Basic markdown support
        if (typeof marked !== 'undefined') {
            return marked.parse(content);
        }
        
        // Fallback: simple formatting
        return content
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>');
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // UI Helper Methods
    updateStatus(type, message) {
        const statusIndicator = document.getElementById('statusIndicator');
        const statusDot = statusIndicator.querySelector('.status-dot');
        const statusText = statusIndicator.querySelector('.status-text');

        statusDot.className = `status-dot ${type}`;
        statusText.textContent = message;
    }

    enableInput() {
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');

        messageInput.disabled = false;
        sendBtn.disabled = false;
        messageInput.placeholder = 'Type your message here... (Press Enter to send, Shift+Enter for new line)';
    }

    disableInput() {
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');

        messageInput.disabled = true;
        sendBtn.disabled = true;
        messageInput.placeholder = 'Please wait...';
    }

    showTypingIndicator() {
        const typingIndicator = document.getElementById('typingIndicator');
        typingIndicator.style.display = 'flex';
    }

    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typingIndicator');
        typingIndicator.style.display = 'none';
    }

    showLoadingOverlay() {
        const overlay = document.getElementById('loadingOverlay');
        overlay.classList.remove('hidden');
    }

    hideLoadingOverlay() {
        const overlay = document.getElementById('loadingOverlay');
        overlay.classList.add('hidden');
    }

    autoResizeTextarea(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }

    scrollToBottom() {
        const messagesContainer = document.getElementById('messagesContainer');
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    clearMessages() {
        const messagesContainer = document.getElementById('messagesContainer');
        messagesContainer.innerHTML = `
            <div class="welcome-message">
                <div class="welcome-content">
                    <h1>🤖 Welcome to Featureimanbug AI</h1>
                    <p>Your personal GPT-2 powered assistant is ready to help!</p>
                    <p class="start-prompt">Start a conversation by typing a message below!</p>
                </div>
            </div>
        `;
    }

    updateChatTitle(title) {
        document.getElementById('chatTitle').textContent = title;
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('open');
    }

    // Conversation Management
    loadConversations() {
        this.socket.emit('get_conversations');
    }

    updateConversationsList(conversations) {
        this.conversations = conversations;
        const conversationsList = document.getElementById('conversationsList');

        conversationsList.innerHTML = '';

        conversations.forEach(conv => {
            const item = document.createElement('div');
            item.className = 'conversation-item';
            if (conv.conversation_id === this.currentConversationId) {
                item.classList.add('active');
            }

            item.innerHTML = `
                <div class="conversation-title">${conv.title}</div>
                <div class="conversation-meta">${conv.message_count} messages</div>
            `;

            item.addEventListener('click', () => {
                this.switchConversation(conv.conversation_id);
            });

            conversationsList.appendChild(item);
        });
    }

    createNewConversation() {
        const title = prompt('Enter conversation title (optional):') || 'New Conversation';
        this.socket.emit('create_conversation', { title });
    }

    switchConversation(conversationId) {
        if (conversationId === this.currentConversationId) return;

        this.socket.emit('switch_conversation', { conversation_id: conversationId });
    }

    loadConversationMessages(conversation) {
        this.clearMessages();

        if (conversation && conversation.messages) {
            conversation.messages.forEach(msg => {
                if (msg.role !== 'system') {
                    this.addMessage(msg.role, msg.content);
                }
            });
        }

        this.scrollToBottom();
    }

    // Settings are now preset for optimal conversation
    // No manual settings needed - using balanced parameters

    // Export functionality
    openExportModal() {
        const modal = document.getElementById('exportModal');
        modal.classList.add('show');
    }

    closeExportModal() {
        const modal = document.getElementById('exportModal');
        modal.classList.remove('show');
    }

    exportConversations() {
        const format = document.getElementById('exportFormat').value;

        fetch('/api/export', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ format })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showSuccess(`Conversations exported to ${data.filename}`);
                // In a real implementation, you might trigger a download here
            } else {
                this.showError('Export failed: ' + data.error);
            }
        })
        .catch(error => {
            this.showError('Export failed: ' + error.message);
        });

        this.closeExportModal();
    }

    // Notification methods
    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type = 'info') {
        // Simple notification system
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 3000;
            animation: slideInRight 0.3s ease;
            max-width: 300px;
            word-wrap: break-word;
        `;

        if (type === 'success') {
            notification.style.backgroundColor = '#10b981';
        } else if (type === 'error') {
            notification.style.backgroundColor = '#ef4444';
        } else {
            notification.style.backgroundColor = '#3b82f6';
        }

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.featureimanbugAI = new FeatureimanbugAI();
});

// Add CSS animations for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
