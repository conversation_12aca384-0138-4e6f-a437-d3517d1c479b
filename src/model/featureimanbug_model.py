"""
Featureimanbug AI - Enhanced Transformer Language Model

This module implements an improved transformer architecture with modern techniques
for better performance and training stability.
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Optional, Tuple, Dict, Any
from dataclasses import dataclass

from ..config import GPTConfig


@dataclass
class FeatureimanbugConfig:
    """Configuration for Featureimanbug AI model."""
    vocab_size: int = 50257
    context_length: int = 1024
    emb_dim: int = 768
    n_heads: int = 12
    n_layers: int = 12
    drop_rate: float = 0.1
    qkv_bias: bool = True
    
    # Enhanced features
    use_flash_attention: bool = False
    use_rotary_pos_emb: bool = True
    use_glu_activation: bool = True
    use_layer_scale: bool = True
    layer_scale_init: float = 1e-4
    use_pre_norm: bool = True
    
    # Training settings
    learning_rate: float = 3e-4
    batch_size: int = 8
    max_epochs: int = 10
    warmup_steps: int = 1000
    weight_decay: float = 0.01
    
    # Generation settings
    max_new_tokens: int = 100
    temperature: float = 0.8
    top_k: int = 50
    top_p: float = 0.9


class RotaryPositionalEmbedding(nn.Module):
    """Rotary Positional Embedding (RoPE) for better position encoding."""
    
    def __init__(self, dim: int, max_seq_len: int = 8192):
        super().__init__()
        self.dim = dim
        self.max_seq_len = max_seq_len
        
        # Precompute frequencies
        inv_freq = 1.0 / (10000 ** (torch.arange(0, dim, 2).float() / dim))
        self.register_buffer('inv_freq', inv_freq)
        
        # Cache for efficiency
        self._cached_cos = None
        self._cached_sin = None
        self._cached_seq_len = 0
    
    def _update_cache(self, seq_len: int, device: torch.device):
        """Update cached cos/sin values."""
        if seq_len > self._cached_seq_len:
            self._cached_seq_len = seq_len
            t = torch.arange(seq_len, device=device, dtype=self.inv_freq.dtype)
            freqs = torch.outer(t, self.inv_freq)
            emb = torch.cat((freqs, freqs), dim=-1)
            self._cached_cos = emb.cos()
            self._cached_sin = emb.sin()
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Apply rotary positional embedding."""
        seq_len = x.shape[-2]
        self._update_cache(seq_len, x.device)
        
        cos = self._cached_cos[:seq_len]
        sin = self._cached_sin[:seq_len]
        
        return cos, sin


def apply_rotary_pos_emb(q: torch.Tensor, k: torch.Tensor, cos: torch.Tensor, sin: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
    """Apply rotary positional embedding to query and key tensors."""
    def rotate_half(x):
        x1, x2 = x.chunk(2, dim=-1)
        return torch.cat((-x2, x1), dim=-1)
    
    q_embed = (q * cos) + (rotate_half(q) * sin)
    k_embed = (k * cos) + (rotate_half(k) * sin)
    
    return q_embed, k_embed


class GLUFeedForward(nn.Module):
    """Gated Linear Unit (GLU) feed-forward network for better performance."""
    
    def __init__(self, emb_dim: int, hidden_dim: Optional[int] = None, drop_rate: float = 0.1):
        super().__init__()
        hidden_dim = hidden_dim or 4 * emb_dim
        
        self.gate_proj = nn.Linear(emb_dim, hidden_dim, bias=False)
        self.up_proj = nn.Linear(emb_dim, hidden_dim, bias=False)
        self.down_proj = nn.Linear(hidden_dim, emb_dim, bias=False)
        self.dropout = nn.Dropout(drop_rate)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        gate = F.silu(self.gate_proj(x))  # SiLU activation
        up = self.up_proj(x)
        hidden = gate * up
        hidden = self.dropout(hidden)
        return self.down_proj(hidden)


class EnhancedMultiHeadAttention(nn.Module):
    """Enhanced multi-head attention with modern improvements."""
    
    def __init__(self, config: FeatureimanbugConfig):
        super().__init__()
        assert config.emb_dim % config.n_heads == 0
        
        self.emb_dim = config.emb_dim
        self.n_heads = config.n_heads
        self.head_dim = config.emb_dim // config.n_heads
        self.scale = self.head_dim ** -0.5
        self.use_rotary_pos_emb = config.use_rotary_pos_emb
        
        # Query, Key, Value projections
        self.W_query = nn.Linear(config.emb_dim, config.emb_dim, bias=config.qkv_bias)
        self.W_key = nn.Linear(config.emb_dim, config.emb_dim, bias=config.qkv_bias)
        self.W_value = nn.Linear(config.emb_dim, config.emb_dim, bias=config.qkv_bias)
        
        # Output projection
        self.out_proj = nn.Linear(config.emb_dim, config.emb_dim)
        
        # Dropout
        self.dropout = nn.Dropout(config.drop_rate)
        
        # Rotary positional embedding
        if self.use_rotary_pos_emb:
            self.rotary_emb = RotaryPositionalEmbedding(self.head_dim)
        
        # Causal mask
        self.register_buffer(
            "mask",
            torch.tril(torch.ones(config.context_length, config.context_length))
            .view(1, 1, config.context_length, config.context_length)
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        batch_size, seq_len, emb_dim = x.shape
        
        # Compute Q, K, V
        q = self.W_query(x)
        k = self.W_key(x)
        v = self.W_value(x)
        
        # Reshape for multi-head attention
        q = q.view(batch_size, seq_len, self.n_heads, self.head_dim).transpose(1, 2)
        k = k.view(batch_size, seq_len, self.n_heads, self.head_dim).transpose(1, 2)
        v = v.view(batch_size, seq_len, self.n_heads, self.head_dim).transpose(1, 2)
        
        # Apply rotary positional embedding
        if self.use_rotary_pos_emb:
            cos, sin = self.rotary_emb(q)
            q, k = apply_rotary_pos_emb(q, k, cos, sin)
        
        # Compute attention scores
        attn_scores = torch.matmul(q, k.transpose(-2, -1)) * self.scale
        
        # Apply causal mask
        mask = self.mask[:, :, :seq_len, :seq_len]
        attn_scores = attn_scores.masked_fill(mask == 0, float('-inf'))
        
        # Apply softmax and dropout
        attn_weights = F.softmax(attn_scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # Apply attention to values
        attn_output = torch.matmul(attn_weights, v)
        
        # Reshape and project output
        attn_output = attn_output.transpose(1, 2).contiguous().view(
            batch_size, seq_len, emb_dim
        )
        
        return self.out_proj(attn_output)


class LayerScale(nn.Module):
    """Layer Scale for better training stability."""
    
    def __init__(self, dim: int, init_value: float = 1e-4):
        super().__init__()
        self.scale = nn.Parameter(torch.ones(dim) * init_value)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return x * self.scale


class EnhancedTransformerBlock(nn.Module):
    """Enhanced transformer block with modern improvements."""
    
    def __init__(self, config: FeatureimanbugConfig):
        super().__init__()
        self.use_pre_norm = config.use_pre_norm
        self.use_layer_scale = config.use_layer_scale
        self.use_glu_activation = config.use_glu_activation
        
        # Attention
        self.att = EnhancedMultiHeadAttention(config)
        
        # Feed-forward
        if config.use_glu_activation:
            self.ff = GLUFeedForward(config.emb_dim, drop_rate=config.drop_rate)
        else:
            self.ff = nn.Sequential(
                nn.Linear(config.emb_dim, 4 * config.emb_dim),
                nn.GELU(),
                nn.Linear(4 * config.emb_dim, config.emb_dim),
                nn.Dropout(config.drop_rate)
            )
        
        # Layer normalization
        self.norm1 = nn.LayerNorm(config.emb_dim)
        self.norm2 = nn.LayerNorm(config.emb_dim)
        
        # Layer scale
        if config.use_layer_scale:
            self.layer_scale1 = LayerScale(config.emb_dim, config.layer_scale_init)
            self.layer_scale2 = LayerScale(config.emb_dim, config.layer_scale_init)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        if self.use_pre_norm:
            # Pre-norm architecture (more stable)
            if self.use_layer_scale:
                x = x + self.layer_scale1(self.att(self.norm1(x)))
                x = x + self.layer_scale2(self.ff(self.norm2(x)))
            else:
                x = x + self.att(self.norm1(x))
                x = x + self.ff(self.norm2(x))
        else:
            # Post-norm architecture (original)
            if self.use_layer_scale:
                x = self.norm1(x + self.layer_scale1(self.att(x)))
                x = self.norm2(x + self.layer_scale2(self.ff(x)))
            else:
                x = self.norm1(x + self.att(x))
                x = self.norm2(x + self.ff(x))
        
        return x


class FeatureimanbugModel(nn.Module):
    """
    Featureimanbug AI - Enhanced Transformer Language Model
    
    An improved transformer architecture with modern techniques for better
    performance, training stability, and generation quality.
    """
    
    def __init__(self, config: FeatureimanbugConfig):
        super().__init__()
        self.config = config
        
        # Token and position embeddings
        self.tok_emb = nn.Embedding(config.vocab_size, config.emb_dim)
        if not config.use_rotary_pos_emb:
            self.pos_emb = nn.Embedding(config.context_length, config.emb_dim)
        
        # Transformer blocks
        self.trf_blocks = nn.ModuleList([
            EnhancedTransformerBlock(config) for _ in range(config.n_layers)
        ])
        
        # Final layer norm
        self.final_norm = nn.LayerNorm(config.emb_dim)
        
        # Output head
        self.out_head = nn.Linear(config.emb_dim, config.vocab_size, bias=False)
        
        # Tie weights (share embeddings with output layer)
        self.out_head.weight = self.tok_emb.weight
        
        # Dropout
        self.dropout = nn.Dropout(config.drop_rate)
        
        # Initialize weights
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        """Initialize weights with improved initialization."""
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)
    
    def forward(self, input_ids: torch.Tensor, targets: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """Forward pass through the model."""
        batch_size, seq_len = input_ids.shape
        
        # Token embeddings
        tok_emb = self.tok_emb(input_ids)
        
        # Position embeddings (if not using RoPE)
        if not self.config.use_rotary_pos_emb:
            pos_ids = torch.arange(seq_len, device=input_ids.device)
            pos_emb = self.pos_emb(pos_ids)
            x = tok_emb + pos_emb
        else:
            x = tok_emb
        
        # Apply dropout
        x = self.dropout(x)
        
        # Pass through transformer blocks
        for block in self.trf_blocks:
            x = block(x)
        
        # Final layer norm
        x = self.final_norm(x)
        
        # Output projection
        logits = self.out_head(x)
        
        # Compute loss if targets provided
        loss = None
        if targets is not None:
            loss = F.cross_entropy(
                logits.view(-1, logits.size(-1)), 
                targets.view(-1), 
                ignore_index=-1
            )
        
        return logits, loss
    
    def get_num_params(self) -> int:
        """Get the number of parameters in the model."""
        return sum(p.numel() for p in self.parameters())
    
    @torch.no_grad()
    def generate(self, input_ids: torch.Tensor, max_new_tokens: int = 100, 
                 temperature: float = 0.8, top_k: Optional[int] = None, 
                 top_p: Optional[float] = None) -> torch.Tensor:
        """Generate text using the model."""
        self.eval()
        
        for _ in range(max_new_tokens):
            # Get predictions for the last token
            logits, _ = self.forward(input_ids)
            logits = logits[:, -1, :] / temperature
            
            # Apply top-k filtering
            if top_k is not None:
                v, _ = torch.topk(logits, min(top_k, logits.size(-1)))
                logits[logits < v[:, [-1]]] = -float('inf')
            
            # Apply top-p filtering
            if top_p is not None:
                sorted_logits, sorted_indices = torch.sort(logits, descending=True)
                cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)
                
                # Remove tokens with cumulative probability above the threshold
                sorted_indices_to_remove = cumulative_probs > top_p
                sorted_indices_to_remove[..., 1:] = sorted_indices_to_remove[..., :-1].clone()
                sorted_indices_to_remove[..., 0] = 0
                
                indices_to_remove = sorted_indices_to_remove.scatter(1, sorted_indices, sorted_indices_to_remove)
                logits[indices_to_remove] = -float('inf')
            
            # Sample from the distribution
            probs = F.softmax(logits, dim=-1)
            next_token = torch.multinomial(probs, num_samples=1)
            
            # Append to the sequence
            input_ids = torch.cat([input_ids, next_token], dim=1)
            
            # Stop if we exceed context length
            if input_ids.size(1) >= self.config.context_length:
                break
        
        return input_ids
