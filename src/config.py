"""
Configuration management for GPT model.
"""
from dataclasses import dataclass
from typing import Dict, Any
import json
import os


@dataclass
class GPTConfig:
    """Configuration class for GPT model."""
    vocab_size: int = 50257
    context_length: int = 1024
    emb_dim: int = 768
    n_heads: int = 12
    n_layers: int = 12
    drop_rate: float = 0.1
    qkv_bias: bool = False
    
    # Training specific
    learning_rate: float = 3e-4
    batch_size: int = 8
    max_epochs: int = 10
    warmup_steps: int = 1000
    weight_decay: float = 0.01
    
    # Generation specific
    max_new_tokens: int = 100
    temperature: float = 1.0
    top_k: int = 50
    top_p: float = 0.9
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        return {
            'vocab_size': self.vocab_size,
            'context_length': self.context_length,
            'emb_dim': self.emb_dim,
            'n_heads': self.n_heads,
            'n_layers': self.n_layers,
            'drop_rate': self.drop_rate,
            'qkv_bias': self.qkv_bias,
            'learning_rate': self.learning_rate,
            'batch_size': self.batch_size,
            'max_epochs': self.max_epochs,
            'warmup_steps': self.warmup_steps,
            'weight_decay': self.weight_decay,
            'max_new_tokens': self.max_new_tokens,
            'temperature': self.temperature,
            'top_k': self.top_k,
            'top_p': self.top_p
        }

    def __getitem__(self, key: str):
        """Allow dictionary-style access for backward compatibility."""
        return getattr(self, key)
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'GPTConfig':
        """Create config from dictionary."""
        return cls(**config_dict)
    
    def save(self, filepath: str) -> None:
        """Save config to JSON file."""
        with open(filepath, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    @classmethod
    def load(cls, filepath: str) -> 'GPTConfig':
        """Load config from JSON file."""
        with open(filepath, 'r') as f:
            config_dict = json.load(f)
        return cls.from_dict(config_dict)


# Predefined configurations
GPT_CONFIG_124M = GPTConfig(
    vocab_size=50257,
    context_length=1024,
    emb_dim=768,
    n_heads=12,
    n_layers=12,
    drop_rate=0.1,
    qkv_bias=False
)

GPT_CONFIG_355M = GPTConfig(
    vocab_size=50257,
    context_length=1024,
    emb_dim=1024,
    n_heads=16,
    n_layers=24,
    drop_rate=0.1,
    qkv_bias=False
)

GPT_CONFIG_SMALL = GPTConfig(
    vocab_size=50257,
    context_length=256,
    emb_dim=384,
    n_heads=6,
    n_layers=6,
    drop_rate=0.1,
    qkv_bias=False
)
