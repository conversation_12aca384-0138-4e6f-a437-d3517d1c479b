"""
Featureimanbug AI - Advanced text generation strategies.
"""
import torch
import torch.nn.functional as F
from typing import Op<PERSON>, Tu<PERSON>, List
from abc import ABC, abstractmethod
import heapq


class GenerationStrategy(ABC):
    """Abstract base class for text generation strategies."""

    @abstractmethod
    def sample(self, logits: torch.Tensor) -> torch.Tensor:
        """Sample next token from logits."""
        pass


class GreedyDecoding(GenerationStrategy):
    """Greedy decoding - always select the most likely token."""

    def sample(self, logits: torch.Tensor) -> torch.Tensor:
        return torch.argmax(logits, dim=-1, keepdim=True)


class TemperatureSampling(GenerationStrategy):
    """Temperature-based sampling for controlling randomness."""

    def __init__(self, temperature: float = 1.0):
        self.temperature = temperature

    def sample(self, logits: torch.Tensor) -> torch.Tensor:
        if self.temperature == 0:
            return torch.argmax(logits, dim=-1, keepdim=True)

        scaled_logits = logits / self.temperature
        probs = F.softmax(scaled_logits, dim=-1)
        return torch.multinomial(probs, num_samples=1)


class TopKSampling(GenerationStrategy):
    """Top-k sampling - sample from k most likely tokens."""

    def __init__(self, k: int = 50, temperature: float = 1.0):
        self.k = k
        self.temperature = temperature

    def sample(self, logits: torch.Tensor) -> torch.Tensor:
        scaled_logits = logits / self.temperature

        # Get top-k values and indices
        top_k_logits, top_k_indices = torch.topk(scaled_logits, self.k, dim=-1)

        # Create a mask for top-k tokens
        top_k_probs = F.softmax(top_k_logits, dim=-1)

        # Sample from top-k distribution
        sampled_indices = torch.multinomial(top_k_probs, num_samples=1)

        # Map back to original vocabulary
        return torch.gather(top_k_indices, -1, sampled_indices)


class TopPSampling(GenerationStrategy):
    """Top-p (nucleus) sampling - sample from tokens with cumulative probability <= p."""

    def __init__(self, p: float = 0.9, temperature: float = 1.0):
        self.p = p
        self.temperature = temperature

    def sample(self, logits: torch.Tensor) -> torch.Tensor:
        scaled_logits = logits / self.temperature

        # Sort logits in descending order
        sorted_logits, sorted_indices = torch.sort(scaled_logits, descending=True, dim=-1)

        # Calculate cumulative probabilities
        sorted_probs = F.softmax(sorted_logits, dim=-1)
        cumulative_probs = torch.cumsum(sorted_probs, dim=-1)

        # Create mask for tokens to keep (cumulative prob <= p)
        sorted_indices_to_remove = cumulative_probs > self.p
        sorted_indices_to_remove[..., 1:] = sorted_indices_to_remove[..., :-1].clone()
        sorted_indices_to_remove[..., 0] = 0  # Keep at least one token

        # Apply mask
        sorted_logits[sorted_indices_to_remove] = -float('inf')

        # Sample from filtered distribution
        filtered_probs = F.softmax(sorted_logits, dim=-1)
        sampled_sorted_indices = torch.multinomial(filtered_probs, num_samples=1)

        # Map back to original vocabulary
        return torch.gather(sorted_indices, -1, sampled_sorted_indices)


class BeamSearchNode:
    """Node for beam search algorithm."""
    
    def __init__(self, sequence: List[int], score: float, hidden_state=None):
        self.sequence = sequence
        self.score = score
        self.hidden_state = hidden_state
    
    def __lt__(self, other):
        return self.score < other.score


class BeamSearch(GenerationStrategy):
    """Beam search for finding high-probability sequences."""
    
    def __init__(self, beam_width: int = 5, length_penalty: float = 1.0):
        self.beam_width = beam_width
        self.length_penalty = length_penalty
        self.beams = []
    
    def sample(self, logits: torch.Tensor) -> torch.Tensor:
        """Note: This is a simplified interface. Full beam search requires sequence-level generation."""
        # For compatibility with the interface, return top beam candidate
        probs = F.softmax(logits, dim=-1)
        top_probs, top_indices = torch.topk(probs, self.beam_width, dim=-1)
        
        # Return the most likely token (simplified)
        return top_indices[:, :1]
    
    def search(self, model, start_tokens: torch.Tensor, max_length: int, 
               end_token: Optional[int] = None) -> List[Tuple[List[int], float]]:
        """
        Perform full beam search to generate sequences.
        
        Args:
            model: The language model
            start_tokens: Initial token sequence
            max_length: Maximum sequence length
            end_token: End-of-sequence token (optional)
            
        Returns:
            List of (sequence, score) tuples sorted by score
        """
        device = start_tokens.device
        batch_size = start_tokens.size(0)
        
        # Initialize beams
        initial_sequence = start_tokens[0].tolist()
        beams = [BeamSearchNode(initial_sequence, 0.0)]
        completed_sequences = []
        
        for step in range(max_length - len(initial_sequence)):
            candidates = []
            
            for beam in beams:
                if end_token is not None and beam.sequence[-1] == end_token:
                    completed_sequences.append((beam.sequence, beam.score))
                    continue
                
                # Get model predictions
                input_ids = torch.tensor([beam.sequence], device=device)
                with torch.no_grad():
                    logits, _ = model(input_ids)
                    logits = logits[0, -1, :]  # Last token logits
                
                # Get top-k candidates
                log_probs = F.log_softmax(logits, dim=-1)
                top_log_probs, top_indices = torch.topk(log_probs, self.beam_width)
                
                for i in range(self.beam_width):
                    token = top_indices[i].item()
                    token_score = top_log_probs[i].item()
                    
                    new_sequence = beam.sequence + [token]
                    new_score = beam.score + token_score
                    
                    # Apply length penalty
                    length_penalty = ((len(new_sequence)) ** self.length_penalty)
                    normalized_score = new_score / length_penalty
                    
                    candidates.append(BeamSearchNode(new_sequence, normalized_score))
            
            # Select top beam_width candidates
            candidates.sort(key=lambda x: x.score, reverse=True)
            beams = candidates[:self.beam_width]
            
            # Check if all beams are completed
            if not beams:
                break
        
        # Add remaining beams to completed sequences
        for beam in beams:
            completed_sequences.append((beam.sequence, beam.score))
        
        # Sort by score and return
        completed_sequences.sort(key=lambda x: x[1], reverse=True)
        return completed_sequences[:self.beam_width]
